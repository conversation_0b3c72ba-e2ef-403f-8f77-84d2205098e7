<?php
/**
 * Portfolio Website Installation Script
 * Run this file once to set up the database and initial data
 */

// Check if already installed
if (file_exists('config/installed.lock')) {
    die('Installation already completed. Delete config/installed.lock to reinstall.');
}

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $db_host = $_POST['db_host'] ?? 'localhost';
    $db_name = $_POST['db_name'] ?? 'portfolio_db';
    $db_user = $_POST['db_user'] ?? 'root';
    $db_pass = $_POST['db_pass'] ?? '';
    
    $admin_username = $_POST['admin_username'] ?? 'admin';
    $admin_email = $_POST['admin_email'] ?? '<EMAIL>';
    $admin_password = $_POST['admin_password'] ?? 'admin123';
    
    try {
        // Connect to MySQL server
        $pdo = new PDO("mysql:host=$db_host", $db_user, $db_pass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create database
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$db_name`");
        $pdo->exec("USE `$db_name`");
        
        // Read and execute schema
        $schema = file_get_contents('database/schema.sql');
        $statements = explode(';', $schema);
        
        foreach ($statements as $statement) {
            $statement = trim($statement);
            if (!empty($statement)) {
                $pdo->exec($statement);
            }
        }
        
        // Create admin user with hashed password
        $hashed_password = password_hash($admin_password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO admin_users (username, email, password) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE password = VALUES(password)");
        $stmt->execute([$admin_username, $admin_email, $hashed_password]);
        
        // Update database config
        $config_content = "<?php
// Database configuration
define('DB_HOST', '$db_host');
define('DB_NAME', '$db_name');
define('DB_USER', '$db_user');
define('DB_PASS', '$db_pass');

class Database {
    private \$host = DB_HOST;
    private \$db_name = DB_NAME;
    private \$username = DB_USER;
    private \$password = DB_PASS;
    private \$conn;

    public function getConnection() {
        \$this->conn = null;
        try {
            \$this->conn = new PDO(\"mysql:host=\" . \$this->host . \";dbname=\" . \$this->db_name, \$this->username, \$this->password);
            \$this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException \$exception) {
            echo \"Connection error: \" . \$exception->getMessage();
        }
        return \$this->conn;
    }
}
?>";
        
        file_put_contents('config/database.php', $config_content);
        
        // Create installation lock file
        file_put_contents('config/installed.lock', date('Y-m-d H:i:s'));
        
        $message = 'Installation completed successfully! You can now access your portfolio website.';
        $message_type = 'success';
        
    } catch (Exception $e) {
        $message = 'Installation failed: ' . $e->getMessage();
        $message_type = 'error';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio Website Installation</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 min-h-screen py-12">
    <div class="max-w-2xl mx-auto px-4">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-cog text-white text-2xl"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-900">Portfolio Website Installation</h1>
                <p class="text-gray-600 mt-2">Set up your database and admin account</p>
            </div>
            
            <?php if ($message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $message_type == 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'; ?>">
                <i class="fas <?php echo $message_type == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
            
            <?php if ($message_type == 'success'): ?>
            <div class="text-center space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a href="index.php" class="bg-blue-500 text-white px-6 py-3 rounded-lg hover:bg-blue-600 transition-colors block">
                        <i class="fas fa-home mr-2"></i>
                        View Website
                    </a>
                    <a href="admin/" class="bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors block">
                        <i class="fas fa-cog mr-2"></i>
                        Admin Panel
                    </a>
                </div>
                <div class="mt-6 p-4 bg-blue-50 rounded-lg">
                    <h3 class="font-semibold text-blue-900 mb-2">Admin Login Details:</h3>
                    <p class="text-blue-800">
                        <strong>Username:</strong> <?php echo htmlspecialchars($admin_username ?? 'admin'); ?><br>
                        <strong>Password:</strong> <?php echo htmlspecialchars($admin_password ?? 'admin123'); ?>
                    </p>
                </div>
            </div>
            <?php endif; ?>
            
            <?php else: ?>
            
            <form method="POST" class="space-y-6">
                <div class="border-b border-gray-200 pb-6">
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Database Configuration</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="db_host" class="block text-sm font-medium text-gray-700 mb-2">
                                Database Host
                            </label>
                            <input type="text" id="db_host" name="db_host" value="localhost" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="db_name" class="block text-sm font-medium text-gray-700 mb-2">
                                Database Name
                            </label>
                            <input type="text" id="db_name" name="db_name" value="portfolio_db" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="db_user" class="block text-sm font-medium text-gray-700 mb-2">
                                Database Username
                            </label>
                            <input type="text" id="db_user" name="db_user" value="root" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="db_pass" class="block text-sm font-medium text-gray-700 mb-2">
                                Database Password
                            </label>
                            <input type="password" id="db_pass" name="db_pass" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                </div>
                
                <div>
                    <h2 class="text-xl font-semibold text-gray-900 mb-4">Admin Account</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="admin_username" class="block text-sm font-medium text-gray-700 mb-2">
                                Admin Username
                            </label>
                            <input type="text" id="admin_username" name="admin_username" value="admin" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div>
                            <label for="admin_email" class="block text-sm font-medium text-gray-700 mb-2">
                                Admin Email
                            </label>
                            <input type="email" id="admin_email" name="admin_email" value="<EMAIL>" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        
                        <div class="md:col-span-2">
                            <label for="admin_password" class="block text-sm font-medium text-gray-700 mb-2">
                                Admin Password
                            </label>
                            <input type="password" id="admin_password" name="admin_password" value="admin123" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                    </div>
                </div>
                
                <div class="pt-6">
                    <button type="submit" 
                            class="w-full bg-blue-500 text-white py-3 px-6 rounded-lg hover:bg-blue-600 transition-colors font-semibold">
                        <i class="fas fa-download mr-2"></i>
                        Install Portfolio Website
                    </button>
                </div>
            </form>
            
            <?php endif; ?>
        </div>
        
        <div class="mt-6 text-center text-gray-600">
            <p class="text-sm">
                Make sure your MySQL server is running and you have the necessary permissions to create databases.
            </p>
        </div>
    </div>
</body>
</html>
