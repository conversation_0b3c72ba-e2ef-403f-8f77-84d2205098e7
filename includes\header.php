<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                        accent: '#F59E0B'
                    },
                    fontFamily: {
                        'montserrat': ['Montserrat', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        .animation-delay-2000 {
            animation-delay: 2s;
        }
        .animation-delay-4000 {
            animation-delay: 4s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-montserrat">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg fixed w-full top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.php" class="text-xl font-bold text-primary">
                        <?php echo SITE_NAME; ?>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <?php
                    $current_page = basename($_SERVER['PHP_SELF']);
                    $nav_items = [
                        'index.php' => ['Home', 'fas fa-home'],
                        'articles.php' => ['Articles', 'fas fa-newspaper'],
                        'article.php' => ['Articles', 'fas fa-newspaper'], // Article page shows Articles as active
                        'contact.php' => ['Contact', 'fas fa-envelope']
                    ];

                    foreach ($nav_items as $page => $info):
                        if ($page == 'article.php') continue; // Skip article.php in main nav
                        $is_active = ($current_page == $page) || ($current_page == 'article.php' && $page == 'articles.php');
                        $class = $is_active ? 'text-primary border-b-2 border-primary' : 'text-gray-700 hover:text-primary';
                    ?>
                    <a href="<?php echo $page; ?>" class="nav-link <?php echo $class; ?> transition-colors pb-1">
                        <i class="<?php echo $info[1]; ?> mr-1"></i>
                        <?php echo $info[0]; ?>
                    </a>
                    <?php endforeach; ?>
                    <a href="admin/index.php" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors">
                        <i class="fas fa-cog mr-1"></i>
                        Admin
                    </a>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <?php foreach ($nav_items as $page => $info):
                    if ($page == 'article.php') continue;
                    $is_active = ($current_page == $page) || ($current_page == 'article.php' && $page == 'articles.php');
                    $class = $is_active ? 'text-primary bg-blue-50' : 'text-gray-700 hover:text-primary hover:bg-gray-50';
                ?>
                <a href="<?php echo $page; ?>" class="block px-3 py-2 <?php echo $class; ?> rounded-md">
                    <i class="<?php echo $info[1]; ?> mr-2"></i>
                    <?php echo $info[0]; ?>
                </a>
                <?php endforeach; ?>
                <a href="admin/index.php" class="block px-3 py-2 text-primary font-medium bg-blue-50 rounded-md">
                    <i class="fas fa-cog mr-2"></i>
                    Admin
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
