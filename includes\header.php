<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                        accent: '#F59E0B'
                    },
                    fontFamily: {
                        'montserrat': ['Montserrat', 'sans-serif'],
                    }
                }
            }
        }
    </script>
    <style>
        .animation-delay-2000 {
            animation-delay: 2s;
        }
        .animation-delay-4000 {
            animation-delay: 4s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        /* Navbar Scroll Effect */
        .navbar-scrolled {
            background: rgba(15, 23, 42, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        /* Smooth transitions for nav items */
        .nav-item {
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-800 font-montserrat">
    <!-- Navigation -->
    <nav class="bg-gradient-to-r from-slate-900 via-purple-900 to-slate-900 backdrop-blur-md border-b border-white/10 fixed w-full top-0 z-50 shadow-2xl">
        <!-- Background Effects -->
        <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
        <div class="absolute top-0 left-0 w-96 h-full bg-gradient-to-r from-blue-500/20 to-transparent"></div>
        <div class="absolute top-0 right-0 w-96 h-full bg-gradient-to-l from-purple-500/20 to-transparent"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <a href="index.php" class="group flex items-center space-x-3">
                        <!-- Logo Icon -->
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-code text-white text-lg"></i>
                        </div>
                        <!-- Brand Text -->
                        <div class="flex flex-col">
                            <span class="text-xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                                <?php echo SITE_NAME; ?>
                            </span>
                            <span class="text-xs text-gray-400 font-medium">Portfolio</span>
                        </div>
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-2">
                    <?php
                    $current_page = basename($_SERVER['PHP_SELF']);
                    $nav_items = [
                        'index.php' => ['Home', 'fas fa-home'],
                        'articles.php' => ['Articles', 'fas fa-newspaper'],
                        'article.php' => ['Articles', 'fas fa-newspaper'], // Article page shows Articles as active
                        'contact.php' => ['Contact', 'fas fa-envelope']
                    ];

                    foreach ($nav_items as $page => $info):
                        if ($page == 'article.php') continue; // Skip article.php in main nav
                        $is_active = ($current_page == $page) || ($current_page == 'article.php' && $page == 'articles.php');
                    ?>
                    <a href="<?php echo $page; ?>" class="group relative px-4 py-2 rounded-xl transition-all duration-300 <?php echo $is_active ? 'bg-white/20 text-white' : 'text-gray-300 hover:text-white hover:bg-white/10'; ?>">
                        <!-- Active Background -->
                        <?php if ($is_active): ?>
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-xl"></div>
                        <?php endif; ?>

                        <!-- Content -->
                        <div class="relative flex items-center space-x-2">
                            <i class="<?php echo $info[1]; ?> text-sm"></i>
                            <span class="font-medium"><?php echo $info[0]; ?></span>
                        </div>

                        <!-- Hover Effect -->
                        <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </a>
                    <?php endforeach; ?>

                    <!-- Admin Button -->
                    <div class="ml-4">
                        <a href="admin/index.php" class="group relative bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-300 hover:scale-105 hover:shadow-lg">
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-cog text-sm group-hover:rotate-90 transition-transform duration-300"></i>
                                <span>Admin</span>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="group relative p-2 rounded-xl bg-white/10 text-gray-300 hover:text-white hover:bg-white/20 transition-all duration-300">
                        <i class="fas fa-bars text-xl group-hover:scale-110 transition-transform duration-300"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-gradient-to-b from-slate-800 to-purple-900 border-t border-white/10 backdrop-blur-md">
            <div class="px-4 pt-4 pb-6 space-y-2">
                <?php foreach ($nav_items as $page => $info):
                    if ($page == 'article.php') continue;
                    $is_active = ($current_page == $page) || ($current_page == 'article.php' && $page == 'articles.php');
                ?>
                <a href="<?php echo $page; ?>" class="group relative block px-4 py-3 rounded-xl transition-all duration-300 <?php echo $is_active ? 'bg-white/20 text-white' : 'text-gray-300 hover:text-white hover:bg-white/10'; ?>">
                    <!-- Active Background -->
                    <?php if ($is_active): ?>
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500/30 to-purple-500/30 rounded-xl"></div>
                    <?php endif; ?>

                    <!-- Content -->
                    <div class="relative flex items-center space-x-3">
                        <i class="<?php echo $info[1]; ?> text-lg"></i>
                        <span class="font-medium"><?php echo $info[0]; ?></span>
                    </div>

                    <!-- Hover Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </a>
                <?php endforeach; ?>

                <!-- Mobile Admin Button -->
                <div class="pt-4 border-t border-white/10">
                    <a href="admin/index.php" class="group relative block bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-cog text-lg group-hover:rotate-90 transition-transform duration-300"></i>
                            <span>Admin Panel</span>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-16">
