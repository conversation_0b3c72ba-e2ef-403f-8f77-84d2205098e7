<?php
/**
 * Test Database Connection
 * Run this file to check if database connection is working
 * Delete this file after testing
 */

echo "<h2>Testing Database Connection...</h2>";

// Test 1: Check if config file exists
if (file_exists('config/database.php')) {
    echo "✅ Config file found<br>";
    require_once 'config/database.php';
} else {
    echo "❌ Config file not found<br>";
    exit;
}

// Test 2: Try to create database connection
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if ($conn) {
        echo "✅ Database connection successful<br>";
        
        // Test 3: Check if tables exist
        $tables = ['admin_users', 'articles', 'contact_messages', 'portfolio_data'];
        
        foreach ($tables as $table) {
            $stmt = $conn->prepare("SHOW TABLES LIKE '$table'");
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                echo "✅ Table '$table' exists<br>";
            } else {
                echo "❌ Table '$table' missing<br>";
            }
        }
        
        // Test 4: Check admin user
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM admin_users");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result['count'] > 0) {
            echo "✅ Admin user exists<br>";
        } else {
            echo "❌ No admin user found<br>";
        }
        
        echo "<br><strong>Database setup is complete!</strong><br>";
        echo "<a href='index.php'>Go to Website</a> | <a href='admin/index.php'>Go to Admin</a>";
        
    } else {
        echo "❌ Database connection failed<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "<br>";
    echo "<br><strong>Setup Instructions:</strong><br>";
    echo "1. Make sure XAMPP/WAMP is running<br>";
    echo "2. Create database 'portfolio_db' in phpMyAdmin<br>";
    echo "3. Import file 'database/schema.sql'<br>";
    echo "4. Check database credentials in config/database.php<br>";
}
?>
