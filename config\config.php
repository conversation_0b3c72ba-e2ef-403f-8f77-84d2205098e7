<?php
session_start();

// Site configuration
define('SITE_NAME', 'My Portfolio');
define('SITE_URL', 'http://localhost/p_a');
define('ADMIN_EMAIL', '<EMAIL>');

// Include database connection
require_once 'database.php';

// Helper functions
function redirect($url) {
    header("Location: " . $url);
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect('admin/login.php');
    }
}

function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function formatDate($date) {
    return date('d M Y', strtotime($date));
}
?>
