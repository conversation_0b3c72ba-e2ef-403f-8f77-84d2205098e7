<?php
require_once 'config/config.php';
require_once 'classes/Portfolio.php';

$page_title = 'Home';
$portfolio = new Portfolio();

// Get portfolio data
$skills = $portfolio->getBySection('skill');
$projects = $portfolio->getBySection('project');
$education = $portfolio->getBySection('education');
$experience = $portfolio->getBySection('experience');
$organizations = $portfolio->getBySection('organization');
$activities = $portfolio->getBySection('activity');

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary to-secondary text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                    <PERSON><PERSON>, saya <span class="text-accent"><PERSON><PERSON></span>
                </h1>
                <p class="text-xl lg:text-2xl mb-8 text-blue-100">
                    Mahasiswa Teknik Informatika & Aktif dalam Organisasi Kampus
                </p>
                <p class="text-lg mb-8 text-blue-100">
                    Saya adalah mahasiswa aktif Program Studi Teknik Informatika di Universitas Maritim Raja Ali Haji, saat ini berada di semester 6. Saya memiliki ketertarikan dalam bidang pengembangan web, analisis sistem, dan desain berbasis pengguna. Selain fokus pada perkuliahan, saya juga aktif berorganisasi sebagai anggota Dewan Perwakilan Mahasiswa (DPM) Fakultas Teknik dan Teknologi Kemaritiman.
                </p>
                <div class="flex flex-wrap gap-4">
                    <a href="#projects" class="bg-accent text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors">
                        View My Work
                    </a>
                    <a href="contact.php" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors">
                        Get In Touch
                    </a>
                </div>
            </div>
            <div class="text-center">
                <div class="relative inline-block">
                    <?php
                    $profile_image = 'assets/images/profile.jpg';
                    if (file_exists($profile_image)):
                    ?>
                    <img src="<?php echo $profile_image; ?>" alt="Profile Photo"
                         class="w-64 h-64 lg:w-80 lg:h-80 rounded-full object-cover border-8 border-white shadow-2xl">
                    <?php else: ?>
                    <div class="w-64 h-64 lg:w-80 lg:h-80 rounded-full bg-gradient-to-br from-primary to-secondary border-8 border-white shadow-2xl flex items-center justify-center">
                        <i class="fas fa-user text-6xl lg:text-8xl text-white"></i>
                    </div>
                    <?php endif; ?>
                    <div class="absolute -bottom-4 -right-4 bg-accent text-gray-900 p-4 rounded-full">
                        <i class="fas fa-code text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section id="about" class="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
    <!-- Background Decorations -->
    <div class="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10"></div>
    <div class="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
    <div class="absolute top-0 right-0 w-72 h-72 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-2000"></div>
    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-72 h-72 bg-gradient-to-br from-indigo-400/20 to-blue-600/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-4000"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-block">
                <h2 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-6">
                    Tentang Saya
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl text-gray-700 max-w-4xl mx-auto mt-8 leading-relaxed">
                Saya adalah mahasiswa Teknik Informatika yang passionate dalam pengembangan teknologi.
                Saya senang mempelajari teknologi web modern dan berkontribusi dalam berbagai proyek pengembangan sistem.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Card 1 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/80 backdrop-blur-sm p-8 rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-laptop-code text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Web Development</h3>
                    <p class="text-gray-600 leading-relaxed">Mengembangkan aplikasi web modern dengan teknologi terkini dan best practices</p>
                </div>
            </div>

            <!-- Card 2 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/80 backdrop-blur-sm p-8 rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Analisis Sistem</h3>
                    <p class="text-gray-600 leading-relaxed">Menganalisis dan merancang sistem informasi yang efisien dan user-friendly</p>
                </div>
            </div>

            <!-- Card 3 -->
            <div class="group relative md:col-span-2 lg:col-span-1">
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/80 backdrop-blur-sm p-8 rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Organisasi Kampus</h3>
                    <p class="text-gray-600 leading-relaxed">Aktif dalam kegiatan organisasi dan pengembangan soft skills melalui DPM</p>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">6</div>
                <div class="text-gray-600 text-sm font-medium">Semester</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">UMRAH</div>
                <div class="text-gray-600 text-sm font-medium">Universitas</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-pink-600 to-indigo-600 bg-clip-text text-transparent">DPM</div>
                <div class="text-gray-600 text-sm font-medium">Organisasi</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">TI</div>
                <div class="text-gray-600 text-sm font-medium">Jurusan</div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section id="projects" class="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
    <div class="absolute top-1/4 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-0 w-96 h-96 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-2000"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-indigo-500/20 to-blue-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-4000"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-block">
                <h2 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent mb-6">
                    Proyek Unggulan
                </h2>
                <div class="w-32 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl text-gray-300 max-w-4xl mx-auto mt-8 leading-relaxed">
                Beberapa proyek dan karya terbaru yang telah saya kembangkan dengan teknologi modern
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php if (empty($projects)): ?>
            <!-- Sample projects if none in database -->

            <!-- Project 1 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <div class="h-48 bg-gradient-to-br from-blue-500 via-cyan-500 to-teal-500 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-shopping-cart text-2xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-cyan-300 transition-colors">E-Commerce Platform</h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">Platform e-commerce lengkap dengan sistem pembayaran dan manajemen produk</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-sm rounded-full font-medium">PHP</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-cyan-500 to-teal-500 text-white text-sm rounded-full font-medium">MySQL</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-teal-500 to-blue-500 text-white text-sm rounded-full font-medium">JavaScript</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="flex items-center text-cyan-400 hover:text-cyan-300 transition-colors font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                            <a href="#" class="flex items-center text-cyan-400 hover:text-cyan-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Demo
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project 2 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <div class="h-48 bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-tasks text-2xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-pink-300 transition-colors">Task Management App</h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">Aplikasi manajemen tugas kolaboratif dengan update real-time</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm rounded-full font-medium">React</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-pink-500 to-rose-500 text-white text-sm rounded-full font-medium">Node.js</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-rose-500 to-purple-500 text-white text-sm rounded-full font-medium">MongoDB</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="flex items-center text-pink-400 hover:text-pink-300 transition-colors font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                            <a href="#" class="flex items-center text-pink-400 hover:text-pink-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Demo
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project 3 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <div class="h-48 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-user-circle text-2xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-emerald-300 transition-colors">Portfolio Website</h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">Website portfolio responsif dengan panel admin yang lengkap</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gradient-to-r from-emerald-500 to-teal-500 text-white text-sm rounded-full font-medium">PHP</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-teal-500 to-cyan-500 text-white text-sm rounded-full font-medium">Tailwind</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-cyan-500 to-emerald-500 text-white text-sm rounded-full font-medium">MySQL</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                            <a href="#" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Demo
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <?php foreach ($projects as $index => $project): ?>
            <?php
            // Define gradient colors for each project
            $gradients = [
                ['from' => 'blue-500', 'to' => 'cyan-500', 'accent' => 'cyan'],
                ['from' => 'purple-500', 'to' => 'pink-500', 'accent' => 'pink'],
                ['from' => 'emerald-500', 'to' => 'teal-500', 'accent' => 'emerald'],
                ['from' => 'orange-500', 'to' => 'red-500', 'accent' => 'orange'],
                ['from' => 'indigo-500', 'to' => 'purple-500', 'accent' => 'indigo'],
                ['from' => 'green-500', 'to' => 'emerald-500', 'accent' => 'green']
            ];
            $gradient = $gradients[$index % count($gradients)];
            ?>
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-<?php echo $gradient['from']; ?> to-<?php echo $gradient['to']; ?> rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <?php if ($project['image_url']): ?>
                    <div class="h-48 relative overflow-hidden">
                        <img src="<?php echo htmlspecialchars($project['image_url']); ?>" alt="<?php echo htmlspecialchars($project['title']); ?>" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>
                    <?php else: ?>
                    <div class="h-48 bg-gradient-to-br from-<?php echo $gradient['from']; ?> via-<?php echo $gradient['to']; ?> to-<?php echo $gradient['from']; ?> relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-code text-2xl"></i>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-<?php echo $gradient['accent']; ?>-300 transition-colors"><?php echo htmlspecialchars($project['title']); ?></h3>
                        <p class="text-gray-300 mb-4 leading-relaxed"><?php echo htmlspecialchars($project['content']); ?></p>
                        <?php if ($project['link_url']): ?>
                        <div class="flex space-x-4">
                            <a href="<?php echo htmlspecialchars($project['link_url']); ?>" class="flex items-center text-<?php echo $gradient['accent']; ?>-400 hover:text-<?php echo $gradient['accent']; ?>-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> View Project
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-16">
            <div class="inline-block p-8 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20">
                <h3 class="text-2xl font-bold text-white mb-4">Tertarik Berkolaborasi?</h3>
                <p class="text-gray-300 mb-6 max-w-2xl">
                    Saya selalu terbuka untuk proyek-proyek menarik dan kolaborasi yang dapat memberikan dampak positif
                </p>
                <a href="contact.php" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-semibold rounded-xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                    <i class="fas fa-envelope mr-3"></i>
                    Hubungi Saya
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Education Section -->
<section id="education" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Pendidikan</h2>
            <p class="text-xl text-gray-600">Latar belakang pendidikan saya</p>
        </div>

        <div class="max-w-4xl mx-auto">
            <?php foreach ($education as $edu): ?>
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 hover:shadow-lg transition-shadow">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-graduation-cap text-white"></i>
                    </div>
                    <div class="flex-grow">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($edu['title']); ?></h3>
                        <p class="text-gray-600"><?php echo htmlspecialchars($edu['content']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Experience Section -->
<section id="experience" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Pengalaman</h2>
            <p class="text-xl text-gray-600">Perjalanan dan pengalaman saya</p>
        </div>

        <div class="max-w-4xl mx-auto">
            <?php foreach ($experience as $exp): ?>
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 hover:shadow-lg transition-shadow border-l-4 border-primary">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-briefcase text-white"></i>
                    </div>
                    <div class="flex-grow">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($exp['title']); ?></h3>
                        <p class="text-gray-600"><?php echo htmlspecialchars($exp['content']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Organization Section -->
<section id="organization" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Organisasi</h2>
            <p class="text-xl text-gray-600">Organisasi dan komunitas yang saya ikuti</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <?php foreach ($organizations as $org): ?>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <div class="flex-grow">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($org['title']); ?></h3>
                        <p class="text-gray-600"><?php echo htmlspecialchars($org['content']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Activities Section -->
<section id="activities" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Aktivitas</h2>
            <p class="text-xl text-gray-600">Kegiatan dan aktivitas yang saya lakukan</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($activities as $activity): ?>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow text-center">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-star text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($activity['title']); ?></h3>
                <p class="text-gray-600"><?php echo htmlspecialchars($activity['content']); ?></p>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<!-- Skills Section -->
<section id="skills" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Keahlian & Teknologi</h2>
            <p class="text-xl text-gray-600">Bahasa pemrograman dan teknologi yang saya kuasai</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <?php foreach ($skills as $skill): ?>
            <div class="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow">
                <div class="text-3xl mb-3">
                    <?php
                    // Add icons based on skill name
                    $skill_icons = [
                        'PHP' => 'fab fa-php',
                        'JavaScript' => 'fab fa-js-square',
                        'MySQL' => 'fas fa-database',
                        'HTML/CSS' => 'fab fa-html5',
                        'React' => 'fab fa-react',
                        'Laravel' => 'fas fa-code',
                        'Node.js' => 'fab fa-node-js'
                    ];
                    $icon = isset($skill_icons[$skill['title']]) ? $skill_icons[$skill['title']] : 'fas fa-code';
                    ?>
                    <i class="<?php echo $icon; ?> text-primary"></i>
                </div>
                <h3 class="font-semibold text-lg mb-2"><?php echo htmlspecialchars($skill['title']); ?></h3>
                <p class="text-gray-600 text-sm"><?php echo htmlspecialchars($skill['content']); ?></p>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
