<?php
require_once 'config/config.php';
require_once 'classes/Portfolio.php';

$page_title = 'Home';
$portfolio = new Portfolio();

// Get portfolio data
$skills = $portfolio->getBySection('skill');
$projects = $portfolio->getBySection('project');
$education = $portfolio->getBySection('education');
$experience = $portfolio->getBySection('experience');
$organizations = $portfolio->getBySection('organization');
$activities = $portfolio->getBySection('activity');

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary to-secondary text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                    <PERSON><PERSON>, saya <span class="text-accent"><PERSON><PERSON></span>
                </h1>
                <p class="text-xl lg:text-2xl mb-8 text-blue-100">
                    Mahasiswa Teknik Informatika & Aktif dalam Organisasi Kampus
                </p>
                <p class="text-lg mb-8 text-blue-100">
                    Saya adalah mahasiswa aktif Program Studi Teknik Informatika di Universitas Maritim Raja Ali Haji, saat ini berada di semester 6. Saya memiliki ketertarikan dalam bidang pengembangan web, analisis sistem, dan desain berbasis pengguna. Selain fokus pada perkuliahan, saya juga aktif berorganisasi sebagai anggota Dewan Perwakilan Mahasiswa (DPM) Fakultas Teknik dan Teknologi Kemaritiman.
                </p>
                <div class="flex flex-wrap gap-4">
                    <a href="#projects" class="bg-accent text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors">
                        View My Work
                    </a>
                    <a href="contact.php" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors">
                        Get In Touch
                    </a>
                </div>
            </div>
            <div class="text-center">
                <div class="relative inline-block">
                    <?php
                    $profile_image = 'assets/images/profile.jpg';
                    if (file_exists($profile_image)):
                    ?>
                    <img src="<?php echo $profile_image; ?>" alt="Profile Photo"
                         class="w-64 h-64 lg:w-80 lg:h-80 rounded-full object-cover border-8 border-white shadow-2xl">
                    <?php else: ?>
                    <div class="w-64 h-64 lg:w-80 lg:h-80 rounded-full bg-gradient-to-br from-primary to-secondary border-8 border-white shadow-2xl flex items-center justify-center">
                        <i class="fas fa-user text-6xl lg:text-8xl text-white"></i>
                    </div>
                    <?php endif; ?>
                    <div class="absolute -bottom-4 -right-4 bg-accent text-gray-900 p-4 rounded-full">
                        <i class="fas fa-code text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section id="about" class="py-20 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 relative overflow-hidden">
    <!-- Background Decorations -->
    <div class="absolute inset-0 bg-gradient-to-r from-blue-400/10 to-purple-400/10"></div>
    <div class="absolute top-0 left-0 w-72 h-72 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
    <div class="absolute top-0 right-0 w-72 h-72 bg-gradient-to-br from-purple-400/20 to-pink-600/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-2000"></div>
    <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-72 h-72 bg-gradient-to-br from-indigo-400/20 to-blue-600/20 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-4000"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-block">
                <h2 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent mb-6">
                    Tentang Saya
                </h2>
                <div class="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl text-gray-700 max-w-4xl mx-auto mt-8 leading-relaxed">
                Saya adalah mahasiswa Teknik Informatika yang passionate dalam pengembangan teknologi.
                Saya senang mempelajari teknologi web modern dan berkontribusi dalam berbagai proyek pengembangan sistem.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Card 1 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/80 backdrop-blur-sm p-8 rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-laptop-code text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Web Development</h3>
                    <p class="text-gray-600 leading-relaxed">Mengembangkan aplikasi web modern dengan teknologi terkini dan best practices</p>
                </div>
            </div>

            <!-- Card 2 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/80 backdrop-blur-sm p-8 rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-chart-line text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Analisis Sistem</h3>
                    <p class="text-gray-600 leading-relaxed">Menganalisis dan merancang sistem informasi yang efisien dan user-friendly</p>
                </div>
            </div>

            <!-- Card 3 -->
            <div class="group relative md:col-span-2 lg:col-span-1">
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-blue-600 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/80 backdrop-blur-sm p-8 rounded-2xl border border-white/20 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-2">
                    <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                        <i class="fas fa-users text-2xl text-white"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Organisasi Kampus</h3>
                    <p class="text-gray-600 leading-relaxed">Aktif dalam kegiatan organisasi dan pengembangan soft skills melalui DPM</p>
                </div>
            </div>
        </div>

        <!-- Stats Section -->
        <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">6</div>
                <div class="text-gray-600 text-sm font-medium">Semester</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">UMRAH</div>
                <div class="text-gray-600 text-sm font-medium">Universitas</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-pink-600 to-indigo-600 bg-clip-text text-transparent">DPM</div>
                <div class="text-gray-600 text-sm font-medium">Organisasi</div>
            </div>
            <div class="text-center">
                <div class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">TI</div>
                <div class="text-gray-600 text-sm font-medium">Jurusan</div>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section id="projects" class="py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600/10 to-purple-600/10"></div>
    <div class="absolute top-1/4 left-0 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse"></div>
    <div class="absolute bottom-1/4 right-0 w-96 h-96 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-2000"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-indigo-500/20 to-blue-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-4000"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-block">
                <h2 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent mb-6">
                    Proyek Unggulan
                </h2>
                <div class="w-32 h-1 bg-gradient-to-r from-cyan-400 to-purple-400 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl text-gray-300 max-w-4xl mx-auto mt-8 leading-relaxed">
                Beberapa proyek dan karya terbaru yang telah saya kembangkan dengan teknologi modern
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php if (empty($projects)): ?>
            <!-- Sample projects if none in database -->

            <!-- Project 1 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <div class="h-48 bg-gradient-to-br from-blue-500 via-cyan-500 to-teal-500 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-shopping-cart text-2xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-cyan-300 transition-colors">E-Commerce Platform</h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">Platform e-commerce lengkap dengan sistem pembayaran dan manajemen produk</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-sm rounded-full font-medium">PHP</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-cyan-500 to-teal-500 text-white text-sm rounded-full font-medium">MySQL</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-teal-500 to-blue-500 text-white text-sm rounded-full font-medium">JavaScript</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="flex items-center text-cyan-400 hover:text-cyan-300 transition-colors font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                            <a href="#" class="flex items-center text-cyan-400 hover:text-cyan-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Demo
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project 2 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <div class="h-48 bg-gradient-to-br from-purple-500 via-pink-500 to-rose-500 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-tasks text-2xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-pink-300 transition-colors">Task Management App</h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">Aplikasi manajemen tugas kolaboratif dengan update real-time</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gradient-to-r from-purple-500 to-pink-500 text-white text-sm rounded-full font-medium">React</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-pink-500 to-rose-500 text-white text-sm rounded-full font-medium">Node.js</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-rose-500 to-purple-500 text-white text-sm rounded-full font-medium">MongoDB</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="flex items-center text-pink-400 hover:text-pink-300 transition-colors font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                            <a href="#" class="flex items-center text-pink-400 hover:text-pink-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Demo
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project 3 -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <div class="h-48 bg-gradient-to-br from-emerald-500 via-teal-500 to-cyan-500 relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-user-circle text-2xl"></i>
                        </div>
                    </div>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-emerald-300 transition-colors">Portfolio Website</h3>
                        <p class="text-gray-300 mb-4 leading-relaxed">Website portfolio responsif dengan panel admin yang lengkap</p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <span class="px-3 py-1 bg-gradient-to-r from-emerald-500 to-teal-500 text-white text-sm rounded-full font-medium">PHP</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-teal-500 to-cyan-500 text-white text-sm rounded-full font-medium">Tailwind</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-cyan-500 to-emerald-500 text-white text-sm rounded-full font-medium">MySQL</span>
                        </div>
                        <div class="flex space-x-4">
                            <a href="#" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors font-medium">
                                <i class="fab fa-github mr-2"></i> Code
                            </a>
                            <a href="#" class="flex items-center text-emerald-400 hover:text-emerald-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> Demo
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <?php foreach ($projects as $index => $project): ?>
            <?php
            // Define gradient colors for each project
            $gradients = [
                ['from' => 'blue-500', 'to' => 'cyan-500', 'accent' => 'cyan'],
                ['from' => 'purple-500', 'to' => 'pink-500', 'accent' => 'pink'],
                ['from' => 'emerald-500', 'to' => 'teal-500', 'accent' => 'emerald'],
                ['from' => 'orange-500', 'to' => 'red-500', 'accent' => 'orange'],
                ['from' => 'indigo-500', 'to' => 'purple-500', 'accent' => 'indigo'],
                ['from' => 'green-500', 'to' => 'emerald-500', 'accent' => 'green']
            ];
            $gradient = $gradients[$index % count($gradients)];
            ?>
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-<?php echo $gradient['from']; ?> to-<?php echo $gradient['to']; ?> rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden hover:bg-white/20 transition-all duration-500 hover:-translate-y-2 hover:scale-105">
                    <?php if ($project['image_url']): ?>
                    <div class="h-48 relative overflow-hidden">
                        <img src="<?php echo htmlspecialchars($project['image_url']); ?>" alt="<?php echo htmlspecialchars($project['title']); ?>" class="w-full h-full object-cover">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    </div>
                    <?php else: ?>
                    <div class="h-48 bg-gradient-to-br from-<?php echo $gradient['from']; ?> via-<?php echo $gradient['to']; ?> to-<?php echo $gradient['from']; ?> relative overflow-hidden">
                        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                        <div class="absolute bottom-4 left-4 text-white">
                            <i class="fas fa-code text-2xl"></i>
                        </div>
                    </div>
                    <?php endif; ?>
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-white mb-3 group-hover:text-<?php echo $gradient['accent']; ?>-300 transition-colors"><?php echo htmlspecialchars($project['title']); ?></h3>
                        <p class="text-gray-300 mb-4 leading-relaxed"><?php echo htmlspecialchars($project['content']); ?></p>
                        <?php if ($project['link_url']): ?>
                        <div class="flex space-x-4">
                            <a href="<?php echo htmlspecialchars($project['link_url']); ?>" class="flex items-center text-<?php echo $gradient['accent']; ?>-400 hover:text-<?php echo $gradient['accent']; ?>-300 transition-colors font-medium">
                                <i class="fas fa-external-link-alt mr-2"></i> View Project
                            </a>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>

        <!-- Call to Action -->
        <div class="text-center mt-16">
            <div class="inline-block p-8 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20">
                <h3 class="text-2xl font-bold text-white mb-4">Tertarik Berkolaborasi?</h3>
                <p class="text-gray-300 mb-6 max-w-2xl">
                    Saya selalu terbuka untuk proyek-proyek menarik dan kolaborasi yang dapat memberikan dampak positif
                </p>
                <a href="contact.php" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-cyan-500 to-blue-500 text-white font-semibold rounded-xl hover:from-cyan-600 hover:to-blue-600 transition-all duration-300 hover:scale-105 hover:shadow-xl">
                    <i class="fas fa-envelope mr-3"></i>
                    Hubungi Saya
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Education Section -->
<section id="education" class="py-20 bg-gradient-to-br from-indigo-50 via-white to-cyan-50 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0 bg-gradient-to-r from-indigo-100/50 to-cyan-100/50"></div>
    <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-indigo-300/20 to-purple-300/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse"></div>
    <div class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-br from-cyan-300/20 to-blue-300/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-2000"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-block">
                <h2 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-cyan-600 bg-clip-text text-transparent mb-6">
                    Perjalanan Pendidikan
                </h2>
                <div class="w-32 h-1 bg-gradient-to-r from-indigo-500 to-cyan-500 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl text-gray-700 max-w-4xl mx-auto mt-8 leading-relaxed">
                Jejak perjalanan pendidikan yang membentuk fondasi pengetahuan dan karakter saya
            </p>
        </div>

        <div class="max-w-5xl mx-auto">
            <!-- Timeline Container -->
            <div class="relative">
                <!-- Timeline Line -->
                <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-1 h-full bg-gradient-to-b from-indigo-500 via-purple-500 to-cyan-500 rounded-full"></div>

                <!-- Education Items -->
                <?php
                $educations = [
                    [
                        'level' => 'Universitas',
                        'name' => 'Universitas Maritim Raja Ali Haji',
                        'program' => 'Teknik Informatika',
                        'year' => '2022-2026',
                        'status' => 'Sedang Berlangsung',
                        'icon' => 'fas fa-university',
                        'gradient' => ['from' => 'indigo-500', 'to' => 'purple-600', 'accent' => 'indigo']
                    ],
                    [
                        'level' => 'SMA',
                        'name' => 'SMA Negeri 16 Batam',
                        'program' => 'Sekolah Menengah Atas',
                        'year' => '2019-2022',
                        'status' => 'Lulus',
                        'icon' => 'fas fa-graduation-cap',
                        'gradient' => ['from' => 'purple-500', 'to' => 'pink-600', 'accent' => 'purple']
                    ],
                    [
                        'level' => 'SMP',
                        'name' => 'SMP Negeri 40 Batam',
                        'program' => 'Sekolah Menengah Pertama',
                        'year' => '2017-2019',
                        'status' => 'Lulus',
                        'icon' => 'fas fa-school',
                        'gradient' => ['from' => 'cyan-500', 'to' => 'blue-600', 'accent' => 'cyan']
                    ],
                    [
                        'level' => 'SD',
                        'name' => 'SD Cyanita Playing Centre',
                        'program' => 'Sekolah Dasar',
                        'year' => '2010-2016',
                        'status' => 'Lulus',
                        'icon' => 'fas fa-child',
                        'gradient' => ['from' => 'emerald-500', 'to' => 'teal-600', 'accent' => 'emerald']
                    ]
                ];

                foreach ($educations as $index => $edu):
                    $isEven = $index % 2 == 0;
                ?>

                <div class="relative flex items-center mb-12 <?php echo $isEven ? 'md:flex-row' : 'md:flex-row-reverse'; ?>">
                    <!-- Timeline Dot -->
                    <div class="absolute left-8 md:left-1/2 transform md:-translate-x-1/2 w-6 h-6 bg-gradient-to-br from-<?php echo $edu['gradient']['from']; ?> to-<?php echo $edu['gradient']['to']; ?> rounded-full border-4 border-white shadow-lg z-10"></div>

                    <!-- Content Card -->
                    <div class="group ml-20 md:ml-0 md:w-5/12 <?php echo $isEven ? 'md:mr-auto md:pr-8' : 'md:ml-auto md:pl-8'; ?>">
                        <div class="relative">
                            <!-- Glow Effect -->
                            <div class="absolute inset-0 bg-gradient-to-r from-<?php echo $edu['gradient']['from']; ?> to-<?php echo $edu['gradient']['to']; ?> rounded-2xl blur opacity-20 group-hover:opacity-30 transition duration-500"></div>

                            <!-- Card -->
                            <div class="relative bg-white/80 backdrop-blur-sm rounded-2xl border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-1 p-6">
                                <!-- Header -->
                                <div class="flex items-start justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="w-12 h-12 bg-gradient-to-br from-<?php echo $edu['gradient']['from']; ?> to-<?php echo $edu['gradient']['to']; ?> rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                                            <i class="<?php echo $edu['icon']; ?> text-white text-lg"></i>
                                        </div>
                                        <div>
                                            <span class="text-sm font-medium text-<?php echo $edu['gradient']['accent']; ?>-600 bg-<?php echo $edu['gradient']['accent']; ?>-100 px-3 py-1 rounded-full">
                                                <?php echo $edu['level']; ?>
                                            </span>
                                        </div>
                                    </div>
                                    <span class="text-sm font-semibold text-gray-500 bg-gray-100 px-3 py-1 rounded-full">
                                        <?php echo $edu['year']; ?>
                                    </span>
                                </div>

                                <!-- Content -->
                                <h3 class="text-xl font-bold text-gray-900 mb-2 group-hover:text-<?php echo $edu['gradient']['accent']; ?>-700 transition-colors">
                                    <?php echo $edu['name']; ?>
                                </h3>
                                <p class="text-gray-600 mb-3 font-medium">
                                    <?php echo $edu['program']; ?>
                                </p>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-<?php echo $edu['gradient']['accent']; ?>-600 font-medium">
                                        Status: <?php echo $edu['status']; ?>
                                    </span>
                                    <?php if ($index == 0): ?>
                                    <span class="text-xs bg-gradient-to-r from-<?php echo $edu['gradient']['from']; ?> to-<?php echo $edu['gradient']['to']; ?> text-white px-3 py-1 rounded-full font-medium animate-pulse">
                                        Semester 6
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php endforeach; ?>
            </div>
        </div>

        <!-- Achievement Stats -->
        <div class="mt-16 text-center">
            <div class="inline-block bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 shadow-xl p-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-cyan-600 bg-clip-text text-transparent mb-6">
                    Pencapaian Pendidikan
                </h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div class="text-center">
                        <div class="text-3xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">16</div>
                        <div class="text-gray-600 text-sm font-medium">Tahun Belajar</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">4</div>
                        <div class="text-gray-600 text-sm font-medium">Jenjang</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold bg-gradient-to-r from-cyan-600 to-blue-600 bg-clip-text text-transparent">6</div>
                        <div class="text-gray-600 text-sm font-medium">Semester</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">2026</div>
                        <div class="text-gray-600 text-sm font-medium">Target Lulus</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Experience & Activities Section -->
<section id="experience" class="py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0 bg-gradient-to-r from-purple-900/50 to-indigo-900/50"></div>
    <div class="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-br from-indigo-500/20 to-cyan-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-2000"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-4000"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-block">
                <h2 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-purple-400 via-pink-400 to-cyan-400 bg-clip-text text-transparent mb-6">
                    Pengalaman & Aktivitas
                </h2>
                <div class="w-40 h-1 bg-gradient-to-r from-purple-400 to-cyan-400 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl text-gray-300 max-w-4xl mx-auto mt-8 leading-relaxed">
                Perjalanan akademik, organisasi, dan pengembangan diri yang membentuk kompetensi saya
            </p>
        </div>

        <div class="max-w-6xl mx-auto">
            <!-- Activities Grid -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">

                <!-- Activity 1: Mahasiswa -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                    <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 hover:bg-white/20 transition-all duration-500 hover:-translate-y-2">
                        <!-- Header -->
                        <div class="flex items-start justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-graduation-cap text-white text-2xl"></i>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-cyan-400 bg-cyan-400/20 px-3 py-1 rounded-full">
                                        AKADEMIK
                                    </span>
                                </div>
                            </div>
                            <span class="text-sm font-semibold text-gray-300 bg-white/10 px-3 py-1 rounded-full">
                                2022 – Sekarang
                            </span>
                        </div>

                        <!-- Content -->
                        <h3 class="text-2xl font-bold text-white mb-3 group-hover:text-cyan-300 transition-colors">
                            Mahasiswa Teknik Informatika
                        </h3>
                        <p class="text-cyan-400 font-semibold mb-4">
                            Universitas Maritim Raja Ali Haji
                        </p>
                        <p class="text-gray-300 leading-relaxed">
                            Saat ini saya menjalani studi S1 di bidang Teknik Informatika. Fokus utama saya adalah mendalami logika pemrograman, pengembangan aplikasi web dan mobile, serta memahami alur kerja sistem informasi modern.
                        </p>

                        <!-- Progress Bar -->
                        <div class="mt-6">
                            <div class="flex justify-between text-sm text-gray-300 mb-2">
                                <span>Progress Studi</span>
                                <span>75%</span>
                            </div>
                            <div class="w-full bg-white/20 rounded-full h-2">
                                <div class="bg-gradient-to-r from-blue-500 to-cyan-500 h-2 rounded-full" style="width: 75%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Activity 2: DPM -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                    <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 hover:bg-white/20 transition-all duration-500 hover:-translate-y-2">
                        <!-- Header -->
                        <div class="flex items-start justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-users text-white text-2xl"></i>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-pink-400 bg-pink-400/20 px-3 py-1 rounded-full">
                                        ORGANISASI
                                    </span>
                                </div>
                            </div>
                            <span class="text-sm font-semibold text-gray-300 bg-white/10 px-3 py-1 rounded-full">
                                2025 – 2026
                            </span>
                        </div>

                        <!-- Content -->
                        <h3 class="text-2xl font-bold text-white mb-3 group-hover:text-pink-300 transition-colors">
                            Anggota DPM FTTK
                        </h3>
                        <p class="text-pink-400 font-semibold mb-4">
                            Biro Humas dan Media Kreatif
                        </p>
                        <p class="text-gray-300 leading-relaxed">
                            Sebagai bagian dari Biro Humas dan Media Kreatif di DPM Fakultas Teknik dan Teknologi Kemaritiman, saya bertanggung jawab dalam hal publikasi kegiatan, pengelolaan konten media sosial, serta mendesain materi visual yang merepresentasikan kegiatan organisasi secara positif dan profesional.
                        </p>

                        <!-- Skills Tags -->
                        <div class="flex flex-wrap gap-2 mt-6">
                            <span class="px-3 py-1 bg-gradient-to-r from-purple-500/30 to-pink-500/30 text-pink-300 text-sm rounded-full border border-pink-400/30">Media Sosial</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-purple-500/30 to-pink-500/30 text-pink-300 text-sm rounded-full border border-pink-400/30">Desain Visual</span>
                            <span class="px-3 py-1 bg-gradient-to-r from-purple-500/30 to-pink-500/30 text-pink-300 text-sm rounded-full border border-pink-400/30">Publikasi</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Full Width Activity 3: Skill Development -->
            <div class="group relative">
                <div class="absolute inset-0 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                <div class="relative bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-8 hover:bg-white/20 transition-all duration-500 hover:-translate-y-2">
                    <!-- Header -->
                    <div class="flex flex-col md:flex-row md:items-start md:justify-between mb-6">
                        <div class="flex items-center mb-4 md:mb-0">
                            <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                                <i class="fas fa-code text-white text-2xl"></i>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-indigo-400 bg-indigo-400/20 px-3 py-1 rounded-full">
                                    PENGEMBANGAN DIRI
                                </span>
                            </div>
                        </div>
                        <span class="text-sm font-semibold text-gray-300 bg-white/10 px-3 py-1 rounded-full">
                            Sedang Berlangsung
                        </span>
                    </div>

                    <!-- Content -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div>
                            <h3 class="text-2xl font-bold text-white mb-3 group-hover:text-indigo-300 transition-colors">
                                Pengembangan Keterampilan Digital
                            </h3>
                            <p class="text-gray-300 leading-relaxed mb-6">
                                Saya aktif belajar dan praktik di bidang desain UI/UX, pembuatan website interaktif, serta pengembangan aplikasi mobile menggunakan Flutter dan Firebase. Tujuan saya adalah mampu membangun solusi digital yang fungsional, menarik, dan user-friendly.
                            </p>
                        </div>

                        <!-- Skills Grid -->
                        <div class="grid grid-cols-2 gap-4">
                            <div class="bg-white/10 rounded-xl p-4 border border-white/20">
                                <i class="fas fa-paint-brush text-indigo-400 text-2xl mb-3"></i>
                                <h4 class="text-white font-semibold mb-2">UI/UX Design</h4>
                                <p class="text-gray-400 text-sm">Desain antarmuka yang intuitif</p>
                            </div>
                            <div class="bg-white/10 rounded-xl p-4 border border-white/20">
                                <i class="fas fa-globe text-purple-400 text-2xl mb-3"></i>
                                <h4 class="text-white font-semibold mb-2">Web Development</h4>
                                <p class="text-gray-400 text-sm">Website interaktif modern</p>
                            </div>
                            <div class="bg-white/10 rounded-xl p-4 border border-white/20">
                                <i class="fas fa-mobile-alt text-cyan-400 text-2xl mb-3"></i>
                                <h4 class="text-white font-semibold mb-2">Mobile Apps</h4>
                                <p class="text-gray-400 text-sm">Flutter & Firebase</p>
                            </div>
                            <div class="bg-white/10 rounded-xl p-4 border border-white/20">
                                <i class="fas fa-lightbulb text-yellow-400 text-2xl mb-3"></i>
                                <h4 class="text-white font-semibold mb-2">Problem Solving</h4>
                                <p class="text-gray-400 text-sm">Solusi digital inovatif</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Summary Stats -->
            <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-blue-400 bg-clip-text text-transparent">4+</div>
                    <div class="text-gray-300 text-sm font-medium">Tahun Kuliah</div>
                </div>
                <div class="text-center bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-pink-400 to-purple-400 bg-clip-text text-transparent">1</div>
                    <div class="text-gray-300 text-sm font-medium">Organisasi Aktif</div>
                </div>
                <div class="text-center bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">5+</div>
                    <div class="text-gray-300 text-sm font-medium">Skill Dikuasai</div>
                </div>
                <div class="text-center bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20 p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">∞</div>
                    <div class="text-gray-300 text-sm font-medium">Semangat Belajar</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Organization Section -->
<section id="organization" class="py-20 bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0 bg-gradient-to-r from-emerald-100/30 to-cyan-100/30"></div>
    <div class="absolute top-1/4 right-0 w-80 h-80 bg-gradient-to-br from-emerald-400/20 to-teal-400/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse"></div>
    <div class="absolute bottom-1/4 left-0 w-80 h-80 bg-gradient-to-br from-teal-400/20 to-cyan-400/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-2000"></div>
    <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-br from-cyan-400/20 to-emerald-400/20 rounded-full mix-blend-multiply filter blur-3xl opacity-70 animate-pulse animation-delay-4000"></div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div class="text-center mb-16">
            <div class="inline-block">
                <h2 class="text-4xl lg:text-5xl font-bold bg-gradient-to-r from-emerald-600 via-teal-600 to-cyan-600 bg-clip-text text-transparent mb-6">
                    Organisasi & Kepemimpinan
                </h2>
                <div class="w-40 h-1 bg-gradient-to-r from-emerald-500 to-cyan-500 mx-auto rounded-full"></div>
            </div>
            <p class="text-xl text-gray-700 max-w-4xl mx-auto mt-8 leading-relaxed">
                Keterlibatan aktif dalam organisasi kampus untuk mengembangkan jiwa kepemimpinan dan kontribusi positif
            </p>
        </div>

        <div class="max-w-6xl mx-auto">
            <!-- Organizations Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">

                <!-- Organization 1: BEM FTTK -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                    <div class="relative bg-white/70 backdrop-blur-sm rounded-2xl border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 p-8">
                        <!-- Header -->
                        <div class="flex items-start justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-university text-white text-2xl"></i>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-emerald-700 bg-emerald-100 px-3 py-1 rounded-full">
                                        EKSEKUTIF
                                    </span>
                                </div>
                            </div>
                            <span class="text-sm font-semibold text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                                2024/2025
                            </span>
                        </div>

                        <!-- Content -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-emerald-700 transition-colors">
                            Anggota BEM FTTK
                        </h3>
                        <p class="text-emerald-700 font-semibold mb-4">
                            Badan Eksekutif Mahasiswa Fakultas Teknik dan Teknologi Kemaritiman
                        </p>
                        <p class="text-gray-700 leading-relaxed mb-6">
                            Sebagai anggota BEM FTTK, saya berkontribusi dalam berbagai program kerja fakultas, membantu menyuarakan aspirasi mahasiswa, dan terlibat dalam kegiatan pengembangan akademik serta non-akademik di lingkungan fakultas.
                        </p>

                        <!-- Achievements -->
                        <div class="space-y-3">
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check-circle text-emerald-500 mr-3"></i>
                                <span>Koordinasi program kerja fakultas</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check-circle text-emerald-500 mr-3"></i>
                                <span>Penyaluran aspirasi mahasiswa</span>
                            </div>
                            <div class="flex items-center text-sm text-gray-600">
                                <i class="fas fa-check-circle text-emerald-500 mr-3"></i>
                                <span>Pengembangan soft skills</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Organization 2: DPM FTTK -->
                <div class="group relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-teal-500 to-cyan-500 rounded-2xl blur opacity-25 group-hover:opacity-40 transition duration-1000 group-hover:duration-200"></div>
                    <div class="relative bg-white/70 backdrop-blur-sm rounded-2xl border border-white/50 shadow-xl hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 p-8">
                        <!-- Header -->
                        <div class="flex items-start justify-between mb-6">
                            <div class="flex items-center">
                                <div class="w-16 h-16 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-2xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300">
                                    <i class="fas fa-users-cog text-white text-2xl"></i>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-teal-700 bg-teal-100 px-3 py-1 rounded-full">
                                        LEGISLATIF
                                    </span>
                                </div>
                            </div>
                            <span class="text-sm font-semibold text-gray-600 bg-gray-100 px-3 py-1 rounded-full">
                                2025/2026
                            </span>
                        </div>

                        <!-- Content -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-teal-700 transition-colors">
                            Biro Humedkraf DPM FTTK
                        </h3>
                        <p class="text-teal-700 font-semibold mb-4">
                            Dewan Perwakilan Mahasiswa - Hubungan Masyarakat & Media Kreatif
                        </p>
                        <p class="text-gray-700 leading-relaxed mb-6">
                            Bertanggung jawab dalam mengelola hubungan masyarakat, publikasi kegiatan, pengelolaan media sosial, dan pembuatan konten kreatif untuk meningkatkan citra positif organisasi mahasiswa fakultas.
                        </p>

                        <!-- Skills & Responsibilities -->
                        <div class="grid grid-cols-2 gap-3">
                            <div class="bg-teal-50 rounded-lg p-3 border border-teal-200">
                                <i class="fas fa-camera text-teal-600 mb-2"></i>
                                <p class="text-sm font-medium text-teal-800">Media Kreatif</p>
                            </div>
                            <div class="bg-teal-50 rounded-lg p-3 border border-teal-200">
                                <i class="fas fa-bullhorn text-teal-600 mb-2"></i>
                                <p class="text-sm font-medium text-teal-800">Publikasi</p>
                            </div>
                            <div class="bg-teal-50 rounded-lg p-3 border border-teal-200">
                                <i class="fas fa-share-alt text-teal-600 mb-2"></i>
                                <p class="text-sm font-medium text-teal-800">Media Sosial</p>
                            </div>
                            <div class="bg-teal-50 rounded-lg p-3 border border-teal-200">
                                <i class="fas fa-handshake text-teal-600 mb-2"></i>
                                <p class="text-sm font-medium text-teal-800">Humas</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Leadership Journey Timeline -->
            <div class="bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 shadow-xl p-8">
                <h3 class="text-2xl font-bold bg-gradient-to-r from-emerald-600 to-cyan-600 bg-clip-text text-transparent mb-8 text-center">
                    Perjalanan Kepemimpinan
                </h3>

                <div class="relative">
                    <!-- Timeline Line -->
                    <div class="absolute left-8 top-0 bottom-0 w-1 bg-gradient-to-b from-emerald-500 to-cyan-500 rounded-full"></div>

                    <!-- Timeline Items -->
                    <div class="space-y-8">
                        <!-- 2024/2025 -->
                        <div class="relative flex items-center">
                            <div class="absolute left-8 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-full border-2 border-white shadow-lg"></div>
                            <div class="ml-20">
                                <div class="bg-emerald-50 rounded-lg p-4 border border-emerald-200">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-bold text-emerald-800">Bergabung dengan BEM FTTK</h4>
                                        <span class="text-sm text-emerald-600 font-medium">2024/2025</span>
                                    </div>
                                    <p class="text-emerald-700 text-sm">Memulai perjalanan organisasi sebagai anggota BEM FTTK</p>
                                </div>
                            </div>
                        </div>

                        <!-- 2025/2026 -->
                        <div class="relative flex items-center">
                            <div class="absolute left-8 transform -translate-x-1/2 w-4 h-4 bg-gradient-to-br from-teal-500 to-cyan-500 rounded-full border-2 border-white shadow-lg"></div>
                            <div class="ml-20">
                                <div class="bg-teal-50 rounded-lg p-4 border border-teal-200">
                                    <div class="flex items-center justify-between mb-2">
                                        <h4 class="font-bold text-teal-800">Bergabung dengan DPM FTTK</h4>
                                        <span class="text-sm text-teal-600 font-medium">2025/2026</span>
                                    </div>
                                    <p class="text-teal-700 text-sm">Mengembangkan kemampuan di bidang humas dan media kreatif</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Organization Stats -->
            <div class="mt-12 grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 shadow-lg p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">2</div>
                    <div class="text-gray-700 text-sm font-medium">Organisasi Aktif</div>
                </div>
                <div class="text-center bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 shadow-lg p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-teal-600 to-cyan-600 bg-clip-text text-transparent">3+</div>
                    <div class="text-gray-700 text-sm font-medium">Tahun Pengalaman</div>
                </div>
                <div class="text-center bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 shadow-lg p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-cyan-600 to-emerald-600 bg-clip-text text-transparent">10+</div>
                    <div class="text-gray-700 text-sm font-medium">Program Kerja</div>
                </div>
                <div class="text-center bg-white/60 backdrop-blur-sm rounded-2xl border border-white/50 shadow-lg p-6">
                    <div class="text-3xl font-bold bg-gradient-to-r from-emerald-600 to-cyan-600 bg-clip-text text-transparent">∞</div>
                    <div class="text-gray-700 text-sm font-medium">Dedikasi</div>
                </div>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<!-- Skills Section -->
<section id="skills" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Keahlian & Teknologi</h2>
            <p class="text-xl text-gray-600">Bahasa pemrograman dan teknologi yang saya kuasai</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <?php foreach ($skills as $skill): ?>
            <div class="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow">
                <div class="text-3xl mb-3">
                    <?php
                    // Add icons based on skill name
                    $skill_icons = [
                        'PHP' => 'fab fa-php',
                        'JavaScript' => 'fab fa-js-square',
                        'MySQL' => 'fas fa-database',
                        'HTML/CSS' => 'fab fa-html5',
                        'React' => 'fab fa-react',
                        'Laravel' => 'fas fa-code',
                        'Node.js' => 'fab fa-node-js'
                    ];
                    $icon = isset($skill_icons[$skill['title']]) ? $skill_icons[$skill['title']] : 'fas fa-code';
                    ?>
                    <i class="<?php echo $icon; ?> text-primary"></i>
                </div>
                <h3 class="font-semibold text-lg mb-2"><?php echo htmlspecialchars($skill['title']); ?></h3>
                <p class="text-gray-600 text-sm"><?php echo htmlspecialchars($skill['content']); ?></p>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
