<?php
require_once 'config/config.php';
require_once 'classes/Portfolio.php';

$page_title = 'Home';
$portfolio = new Portfolio();

// Get portfolio data
$skills = $portfolio->getBySection('skill');
$projects = $portfolio->getBySection('project');
$education = $portfolio->getBySection('education');
$experience = $portfolio->getBySection('experience');
$organizations = $portfolio->getBySection('organization');
$activities = $portfolio->getBySection('activity');

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="bg-gradient-to-br from-primary to-secondary text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl lg:text-6xl font-bold mb-6">
                    Hi, I'm <span class="text-accent">Your Name</span>
                </h1>
                <p class="text-xl lg:text-2xl mb-8 text-blue-100">
                    Full Stack Developer & Tech Enthusiast
                </p>
                <p class="text-lg mb-8 text-blue-100">
                    Passionate about creating innovative web solutions and sharing knowledge through code. 
                    I specialize in modern web technologies and love turning ideas into reality.
                </p>
                <div class="flex flex-wrap gap-4">
                    <a href="#projects" class="bg-accent text-gray-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-400 transition-colors">
                        View My Work
                    </a>
                    <a href="contact.php" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary transition-colors">
                        Get In Touch
                    </a>
                </div>
            </div>
            <div class="text-center">
                <div class="relative inline-block">
                    <?php
                    $profile_image = 'assets/images/profile.jpg';
                    if (file_exists($profile_image)):
                    ?>
                    <img src="<?php echo $profile_image; ?>" alt="Profile Photo"
                         class="w-64 h-64 lg:w-80 lg:h-80 rounded-full object-cover border-8 border-white shadow-2xl">
                    <?php else: ?>
                    <div class="w-64 h-64 lg:w-80 lg:h-80 rounded-full bg-gradient-to-br from-primary to-secondary border-8 border-white shadow-2xl flex items-center justify-center">
                        <i class="fas fa-user text-6xl lg:text-8xl text-white"></i>
                    </div>
                    <?php endif; ?>
                    <div class="absolute -bottom-4 -right-4 bg-accent text-gray-900 p-4 rounded-full">
                        <i class="fas fa-code text-2xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section id="about" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">About Me</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                I'm a passionate developer with expertise in modern web technologies. 
                I love creating efficient, scalable solutions and continuously learning new technologies.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="text-center p-6 bg-gray-50 rounded-lg">
                <i class="fas fa-laptop-code text-4xl text-primary mb-4"></i>
                <h3 class="text-xl font-semibold mb-2">Full Stack Development</h3>
                <p class="text-gray-600">Building complete web applications from frontend to backend</p>
            </div>
            <div class="text-center p-6 bg-gray-50 rounded-lg">
                <i class="fas fa-mobile-alt text-4xl text-primary mb-4"></i>
                <h3 class="text-xl font-semibold mb-2">Responsive Design</h3>
                <p class="text-gray-600">Creating beautiful, mobile-first responsive interfaces</p>
            </div>
            <div class="text-center p-6 bg-gray-50 rounded-lg">
                <i class="fas fa-database text-4xl text-primary mb-4"></i>
                <h3 class="text-xl font-semibold mb-2">Database Design</h3>
                <p class="text-gray-600">Designing efficient and scalable database architectures</p>
            </div>
        </div>
    </div>
</section>

<!-- Projects Section -->
<section id="projects" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Featured Projects</h2>
            <p class="text-xl text-gray-600">Some of my recent work and projects</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php if (empty($projects)): ?>
            <!-- Sample projects if none in database -->
            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div class="h-48 bg-gradient-to-br from-primary to-secondary"></div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2">E-Commerce Platform</h3>
                    <p class="text-gray-600 mb-4">Full-stack e-commerce solution built with PHP and MySQL</p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">PHP</span>
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">MySQL</span>
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">JavaScript</span>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-primary hover:text-secondary">
                            <i class="fab fa-github"></i> Code
                        </a>
                        <a href="#" class="text-primary hover:text-secondary">
                            <i class="fas fa-external-link-alt"></i> Live Demo
                        </a>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div class="h-48 bg-gradient-to-br from-accent to-yellow-600"></div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2">Task Management App</h3>
                    <p class="text-gray-600 mb-4">Collaborative task management application with real-time updates</p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">React</span>
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">Node.js</span>
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">MongoDB</span>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-primary hover:text-secondary">
                            <i class="fab fa-github"></i> Code
                        </a>
                        <a href="#" class="text-primary hover:text-secondary">
                            <i class="fas fa-external-link-alt"></i> Live Demo
                        </a>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <div class="h-48 bg-gradient-to-br from-green-500 to-green-700"></div>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2">Portfolio Website</h3>
                    <p class="text-gray-600 mb-4">Responsive portfolio website with admin panel</p>
                    <div class="flex flex-wrap gap-2 mb-4">
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">PHP</span>
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">Tailwind CSS</span>
                        <span class="px-3 py-1 bg-primary text-white text-sm rounded-full">MySQL</span>
                    </div>
                    <div class="flex space-x-4">
                        <a href="#" class="text-primary hover:text-secondary">
                            <i class="fab fa-github"></i> Code
                        </a>
                        <a href="#" class="text-primary hover:text-secondary">
                            <i class="fas fa-external-link-alt"></i> Live Demo
                        </a>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <?php foreach ($projects as $project): ?>
            <div class="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                <?php if ($project['image_url']): ?>
                <img src="<?php echo htmlspecialchars($project['image_url']); ?>" alt="<?php echo htmlspecialchars($project['title']); ?>" class="w-full h-48 object-cover">
                <?php else: ?>
                <div class="h-48 bg-gradient-to-br from-primary to-secondary"></div>
                <?php endif; ?>
                <div class="p-6">
                    <h3 class="text-xl font-semibold mb-2"><?php echo htmlspecialchars($project['title']); ?></h3>
                    <p class="text-gray-600 mb-4"><?php echo htmlspecialchars($project['content']); ?></p>
                    <?php if ($project['link_url']): ?>
                    <a href="<?php echo htmlspecialchars($project['link_url']); ?>" class="text-primary hover:text-secondary">
                        <i class="fas fa-external-link-alt"></i> View Project
                    </a>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
        </div>
    </div>
</section>

<!-- Education Section -->
<section id="education" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Education</h2>
            <p class="text-xl text-gray-600">My educational background</p>
        </div>

        <div class="max-w-4xl mx-auto">
            <?php foreach ($education as $edu): ?>
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 hover:shadow-lg transition-shadow">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-graduation-cap text-white"></i>
                    </div>
                    <div class="flex-grow">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($edu['title']); ?></h3>
                        <p class="text-gray-600"><?php echo htmlspecialchars($edu['content']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Experience Section -->
<section id="experience" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Experience</h2>
            <p class="text-xl text-gray-600">My professional journey</p>
        </div>

        <div class="max-w-4xl mx-auto">
            <?php foreach ($experience as $exp): ?>
            <div class="bg-white rounded-lg shadow-md p-6 mb-6 hover:shadow-lg transition-shadow border-l-4 border-primary">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-briefcase text-white"></i>
                    </div>
                    <div class="flex-grow">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($exp['title']); ?></h3>
                        <p class="text-gray-600"><?php echo htmlspecialchars($exp['content']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Organization Section -->
<section id="organization" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Organizations</h2>
            <p class="text-xl text-gray-600">Communities and organizations I'm part of</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <?php foreach ($organizations as $org): ?>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="flex items-start">
                    <div class="flex-shrink-0 w-12 h-12 bg-primary rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-users text-white"></i>
                    </div>
                    <div class="flex-grow">
                        <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($org['title']); ?></h3>
                        <p class="text-gray-600"><?php echo htmlspecialchars($org['content']); ?></p>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Activities Section -->
<section id="activities" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Activities</h2>
            <p class="text-xl text-gray-600">What I do in my free time</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($activities as $activity): ?>
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow text-center">
                <div class="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-star text-white text-xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2"><?php echo htmlspecialchars($activity['title']); ?></h3>
                <p class="text-gray-600"><?php echo htmlspecialchars($activity['content']); ?></p>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>

<!-- Skills Section -->
<section id="skills" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">Skills & Technologies</h2>
            <p class="text-xl text-gray-600">Programming languages and technologies I work with</p>
        </div>
        
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            <?php foreach ($skills as $skill): ?>
            <div class="bg-white p-6 rounded-lg shadow-md text-center hover:shadow-lg transition-shadow">
                <div class="text-3xl mb-3">
                    <?php
                    // Add icons based on skill name
                    $skill_icons = [
                        'PHP' => 'fab fa-php',
                        'JavaScript' => 'fab fa-js-square',
                        'MySQL' => 'fas fa-database',
                        'HTML/CSS' => 'fab fa-html5',
                        'React' => 'fab fa-react',
                        'Laravel' => 'fas fa-code',
                        'Node.js' => 'fab fa-node-js'
                    ];
                    $icon = isset($skill_icons[$skill['title']]) ? $skill_icons[$skill['title']] : 'fas fa-code';
                    ?>
                    <i class="<?php echo $icon; ?> text-primary"></i>
                </div>
                <h3 class="font-semibold text-lg mb-2"><?php echo htmlspecialchars($skill['title']); ?></h3>
                <p class="text-gray-600 text-sm"><?php echo htmlspecialchars($skill['content']); ?></p>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
