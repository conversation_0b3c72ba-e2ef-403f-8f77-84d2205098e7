<?php
require_once '../config/config.php';
require_once '../classes/Contact.php';

requireLogin();

$page_title = 'Messages';
$contact = new Contact();

$message = '';
$message_type = '';

// Handle actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['mark_read'])) {
        $id = (int)$_POST['message_id'];
        if ($contact->updateStatus($id, 'read')) {
            $message = 'Message marked as read.';
            $message_type = 'success';
        }
    } elseif (isset($_POST['mark_replied'])) {
        $id = (int)$_POST['message_id'];
        if ($contact->updateStatus($id, 'replied')) {
            $message = 'Message marked as replied.';
            $message_type = 'success';
        }
    } elseif (isset($_POST['delete_message'])) {
        $id = (int)$_POST['message_id'];
        if ($contact->delete($id)) {
            $message = 'Message deleted successfully.';
            $message_type = 'success';
        }
    }
}

// Get messages
$filter = isset($_GET['filter']) ? $_GET['filter'] : 'all';
$messages = $filter == 'all' ? $contact->getAll() : $contact->getAll($filter);

include 'includes/header.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Messages</h1>
        <p class="text-gray-600 mt-2">Manage contact form submissions</p>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="mb-6 p-4 rounded-lg alert-auto-hide <?php echo $message_type == 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'; ?>">
        <i class="fas <?php echo $message_type == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
    <?php endif; ?>

    <!-- Filter Tabs -->
    <div class="mb-6">
        <nav class="flex space-x-8">
            <a href="?filter=all" class="<?php echo $filter == 'all' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                All Messages
            </a>
            <a href="?filter=unread" class="<?php echo $filter == 'unread' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Unread
                <?php 
                $unread_count = $contact->getUnreadCount();
                if ($unread_count > 0): 
                ?>
                <span class="bg-red-100 text-red-800 text-xs font-medium ml-2 px-2.5 py-0.5 rounded-full">
                    <?php echo $unread_count; ?>
                </span>
                <?php endif; ?>
            </a>
            <a href="?filter=read" class="<?php echo $filter == 'read' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Read
            </a>
            <a href="?filter=replied" class="<?php echo $filter == 'replied' ? 'border-primary text-primary' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?> whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm">
                Replied
            </a>
        </nav>
    </div>

    <!-- Messages List -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6">
            <?php if (empty($messages)): ?>
            <div class="text-center py-12">
                <i class="fas fa-inbox text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Messages</h3>
                <p class="text-gray-600">
                    <?php 
                    switch($filter) {
                        case 'unread': echo 'No unread messages at the moment.'; break;
                        case 'read': echo 'No read messages found.'; break;
                        case 'replied': echo 'No replied messages found.'; break;
                        default: echo 'No messages have been received yet.'; break;
                    }
                    ?>
                </p>
            </div>
            <?php else: ?>
            <div class="space-y-4">
                <?php foreach ($messages as $msg): ?>
                <div class="border border-gray-200 rounded-lg p-6 <?php echo $msg['status'] == 'unread' ? 'bg-blue-50 border-blue-200' : 'bg-white'; ?>">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <div class="flex items-center mb-2">
                                <h3 class="text-lg font-semibold text-gray-900 mr-3">
                                    <?php echo htmlspecialchars($msg['name']); ?>
                                </h3>
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?php 
                                    switch($msg['status']) {
                                        case 'unread': echo 'bg-red-100 text-red-800'; break;
                                        case 'read': echo 'bg-yellow-100 text-yellow-800'; break;
                                        case 'replied': echo 'bg-green-100 text-green-800'; break;
                                    }
                                    ?>">
                                    <?php echo ucfirst($msg['status']); ?>
                                </span>
                            </div>
                            
                            <div class="text-sm text-gray-600 mb-2">
                                <i class="fas fa-envelope mr-2"></i>
                                <?php echo htmlspecialchars($msg['email']); ?>
                            </div>
                            
                            <?php if ($msg['subject']): ?>
                            <div class="text-sm text-gray-600 mb-3">
                                <i class="fas fa-tag mr-2"></i>
                                <strong>Subject:</strong> <?php echo htmlspecialchars($msg['subject']); ?>
                            </div>
                            <?php endif; ?>
                            
                            <div class="text-gray-800 mb-4">
                                <?php echo nl2br(htmlspecialchars($msg['message'])); ?>
                            </div>
                            
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-clock mr-1"></i>
                                Received on <?php echo formatDate($msg['created_at']); ?>
                            </div>
                        </div>
                        
                        <div class="ml-4 flex-shrink-0">
                            <div class="flex space-x-2">
                                <?php if ($msg['status'] == 'unread'): ?>
                                <form method="POST" class="inline">
                                    <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                    <button type="submit" name="mark_read" 
                                            class="text-blue-600 hover:text-blue-900 text-sm">
                                        <i class="fas fa-eye"></i> Mark Read
                                    </button>
                                </form>
                                <?php endif; ?>
                                
                                <?php if ($msg['status'] != 'replied'): ?>
                                <form method="POST" class="inline">
                                    <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                    <button type="submit" name="mark_replied" 
                                            class="text-green-600 hover:text-green-900 text-sm">
                                        <i class="fas fa-reply"></i> Mark Replied
                                    </button>
                                </form>
                                <?php endif; ?>
                                
                                <a href="mailto:<?php echo htmlspecialchars($msg['email']); ?>?subject=Re: <?php echo htmlspecialchars($msg['subject'] ?: 'Your message'); ?>" 
                                   class="text-purple-600 hover:text-purple-900 text-sm">
                                    <i class="fas fa-envelope"></i> Reply
                                </a>
                                
                                <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this message?')">
                                    <input type="hidden" name="message_id" value="<?php echo $msg['id']; ?>">
                                    <button type="submit" name="delete_message" 
                                            class="text-red-600 hover:text-red-900 text-sm">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
