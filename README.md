# Portfolio Website

A modern, responsive portfolio website built with PHP, MySQL, and Tailwind CSS. Features a complete admin panel for content management.

## Features

### Frontend
- **Responsive Design**: Mobile-first design using Tailwind CSS
- **Home/Landing Page**: 
  - Hero section with profile photo
  - About section with biodata
  - Skills showcase
  - Featured projects
  - Education timeline
  - Work experience
  - Organizations
  - Activities
- **Articles Page**: Dynamic blog with article listings
- **Individual Article Pages**: Full article view with sharing options
- **Contact Page**: 
  - Social media links
  - Contact form
  - Service fee information

### Admin Panel
- **Secure Authentication**: Login/logout system
- **Dashboard**: Overview with statistics and quick actions
- **Article Management**: Create, edit, delete, and publish articles
- **Message Management**: View and manage contact form submissions
- **Portfolio Management**: Update skills, projects, education, etc.

## Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3 (Tailwind CSS), JavaScript
- **Icons**: Font Awesome 6
- **Server**: Apache with mod_rewrite

## Installation

### Prerequisites
- XAMPP, WAMP, or similar local server environment
- PHP 7.4 or higher
- MySQL 5.7 or higher

### Setup Instructions

1. **Clone/Download the project**
   ```bash
   # If using Git
   git clone [repository-url] portfolio
   
   # Or download and extract to your web server directory
   # For XAMPP: C:\xampp\htdocs\portfolio
   ```

2. **Database Setup**
   - Open phpMyAdmin or your MySQL client
   - Create a new database named `portfolio_db`
   - Import the database schema:
     ```sql
     # Run the SQL commands from database/schema.sql
     ```
   - Or manually run the schema file in phpMyAdmin

3. **Configuration**
   - Open `config/database.php`
   - Update database credentials if needed:
     ```php
     define('DB_HOST', 'localhost');
     define('DB_NAME', 'portfolio_db');
     define('DB_USER', 'root');
     define('DB_PASS', '');
     ```

4. **File Permissions**
   - Ensure the `assets/images/` directory is writable
   - Set appropriate permissions for upload functionality

5. **Access the Website**
   - Frontend: `http://localhost/portfolio/`
   - Admin Panel: `http://localhost/portfolio/admin/`
   - Default admin credentials:
     - Username: `admin`
     - Password: `admin123`

## Usage

### Admin Panel

1. **Login**: Navigate to `/admin/` and use the default credentials
2. **Dashboard**: View statistics and quick actions
3. **Articles**: 
   - Create new articles
   - Edit existing articles
   - Publish/unpublish articles
   - Delete articles
4. **Messages**: View and manage contact form submissions
5. **Portfolio**: Update personal information, skills, projects, etc.

### Customization

1. **Personal Information**
   - Update the hero section in `index.php`
   - Add your profile photo to `assets/images/profile.jpg`
   - Update social media links in the footer

2. **Content Management**
   - Use the admin panel to add articles
   - Update portfolio data through the database or admin interface
   - Customize the contact form and service fees

3. **Styling**
   - Modify Tailwind CSS classes for design changes
   - Update colors in the Tailwind config within header files
   - Add custom CSS if needed

## File Structure

```
portfolio/
├── admin/                  # Admin panel
│   ├── includes/          # Admin header/footer
│   ├── index.php          # Dashboard
│   ├── articles.php       # Article management
│   ├── messages.php       # Message management
│   ├── login.php          # Admin login
│   └── logout.php         # Admin logout
├── assets/
│   └── images/            # Image uploads
├── classes/               # PHP classes
│   ├── Article.php        # Article management
│   ├── Contact.php        # Contact form handling
│   └── Portfolio.php      # Portfolio data
├── config/                # Configuration files
│   ├── config.php         # Main config
│   └── database.php       # Database connection
├── database/
│   └── schema.sql         # Database schema
├── includes/              # Shared includes
│   ├── header.php         # Main header
│   └── footer.php         # Main footer
├── index.php              # Home page
├── articles.php           # Articles listing
├── article.php            # Single article view
├── contact.php            # Contact page
├── .htaccess              # Apache configuration
└── README.md              # This file
```

## Security Features

- Password hashing for admin accounts
- SQL injection prevention using prepared statements
- XSS protection through input sanitization
- CSRF protection considerations
- Secure file upload handling
- Access control for admin areas

## Customization Tips

1. **Colors**: Update the Tailwind color scheme in the header files
2. **Content**: Use the admin panel to manage dynamic content
3. **Images**: Add your photos to the `assets/images/` directory
4. **Social Links**: Update social media URLs in the footer
5. **Contact Info**: Modify contact details in `contact.php`

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `config/database.php`
   - Ensure MySQL service is running
   - Verify database exists

2. **Admin Login Issues**
   - Use default credentials: admin/admin123
   - Check if admin user exists in database
   - Clear browser cache/cookies

3. **Images Not Loading**
   - Check file permissions on `assets/images/`
   - Verify image paths are correct
   - Ensure images exist in the directory

4. **URL Rewriting Issues**
   - Enable mod_rewrite in Apache
   - Check .htaccess file permissions
   - Verify Apache configuration

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Verify all installation steps were completed
3. Check PHP and MySQL error logs
4. Ensure all required PHP extensions are installed

## License

This project is open source and available under the [MIT License](LICENSE).
