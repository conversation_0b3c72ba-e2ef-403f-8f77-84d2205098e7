# Admin Panel .htaccess

# Enable URL Rewriting
RewriteEngine On

# Redirect admin/ to admin/index.php
DirectoryIndex index.php

# Remove .php extension from admin URLs
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^([^\.]+)$ $1.php [NC,L]

# Security - Prevent access to sensitive files
<Files ~ "^\.">
    Order allow,deny
    Deny from all
</Files>

# Prevent access to includes directory
<Files ~ "includes/*">
    Order allow,deny
    Deny from all
</Files>
