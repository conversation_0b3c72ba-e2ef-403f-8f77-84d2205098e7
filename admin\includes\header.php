<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - Admin' : 'Admin Panel'; ?> - <?php echo SITE_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#3B82F6',
                        secondary: '#1E40AF',
                        accent: '#F59E0B'
                    },
                    fontFamily: {
                        'montserrat': ['Montserrat', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-100 font-montserrat">
    <!-- Admin Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="index.php" class="text-xl font-bold text-primary">
                        <i class="fas fa-cog mr-2"></i>
                        Admin Panel
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="index.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'text-primary' : 'text-gray-700 hover:text-primary'; ?> transition-colors">
                        <i class="fas fa-tachometer-alt mr-1"></i> Dashboard
                    </a>
                    <a href="articles.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'articles.php' ? 'text-primary' : 'text-gray-700 hover:text-primary'; ?> transition-colors">
                        <i class="fas fa-newspaper mr-1"></i> Articles
                    </a>
                    <a href="portfolio.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'portfolio.php' ? 'text-primary' : 'text-gray-700 hover:text-primary'; ?> transition-colors">
                        <i class="fas fa-briefcase mr-1"></i> Portfolio
                    </a>
                    <a href="messages.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'messages.php' ? 'text-primary' : 'text-gray-700 hover:text-primary'; ?> transition-colors">
                        <i class="fas fa-envelope mr-1"></i> Messages
                    </a>
                    
                    <!-- User Menu -->
                    <div class="relative">
                        <button id="user-menu-button" class="flex items-center text-gray-700 hover:text-primary">
                            <i class="fas fa-user-circle text-xl mr-2"></i>
                            <?php echo htmlspecialchars($_SESSION['admin_username']); ?>
                            <i class="fas fa-chevron-down ml-2"></i>
                        </button>
                        
                        <div id="user-menu" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                            <a href="../index.php" target="_blank" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-external-link-alt mr-2"></i> View Website
                            </a>
                            <a href="profile.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-user mr-2"></i> Profile
                            </a>
                            <div class="border-t border-gray-100"></div>
                            <a href="logout.php" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-gray-700 hover:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div id="mobile-menu" class="md:hidden hidden bg-white border-t">
            <div class="px-2 pt-2 pb-3 space-y-1">
                <a href="index.php" class="block px-3 py-2 text-gray-700 hover:text-primary">
                    <i class="fas fa-tachometer-alt mr-2"></i> Dashboard
                </a>
                <a href="articles.php" class="block px-3 py-2 text-gray-700 hover:text-primary">
                    <i class="fas fa-newspaper mr-2"></i> Articles
                </a>
                <a href="portfolio.php" class="block px-3 py-2 text-gray-700 hover:text-primary">
                    <i class="fas fa-briefcase mr-2"></i> Portfolio
                </a>
                <a href="messages.php" class="block px-3 py-2 text-gray-700 hover:text-primary">
                    <i class="fas fa-envelope mr-2"></i> Messages
                </a>
                <div class="border-t border-gray-200 pt-2">
                    <a href="../index.php" target="_blank" class="block px-3 py-2 text-gray-700 hover:text-primary">
                        <i class="fas fa-external-link-alt mr-2"></i> View Website
                    </a>
                    <a href="logout.php" class="block px-3 py-2 text-red-600 hover:text-primary">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="py-6">
