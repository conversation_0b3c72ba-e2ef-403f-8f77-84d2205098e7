<?php
require_once '../config/config.php';
require_once '../classes/Article.php';

requireLogin();

$page_title = 'Manage Articles';
$article = new Article();

$message = '';
$message_type = '';

// Handle actions
$action = isset($_GET['action']) ? $_GET['action'] : 'list';
$id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['create_article'])) {
        $title = sanitizeInput($_POST['title']);
        $content = sanitizeInput($_POST['content']);
        $excerpt = sanitizeInput($_POST['excerpt']);
        $status = sanitizeInput($_POST['status']);
        
        if (empty($title) || empty($content)) {
            $message = 'Title and content are required.';
            $message_type = 'error';
        } else {
            if ($article->create($title, $content, $excerpt, '', $status)) {
                $message = 'Article created successfully!';
                $message_type = 'success';
                $action = 'list';
            } else {
                $message = 'Error creating article.';
                $message_type = 'error';
            }
        }
    } elseif (isset($_POST['update_article'])) {
        $title = sanitizeInput($_POST['title']);
        $content = sanitizeInput($_POST['content']);
        $excerpt = sanitizeInput($_POST['excerpt']);
        $status = sanitizeInput($_POST['status']);
        
        if (empty($title) || empty($content)) {
            $message = 'Title and content are required.';
            $message_type = 'error';
        } else {
            if ($article->update($id, $title, $content, $excerpt, '', $status)) {
                $message = 'Article updated successfully!';
                $message_type = 'success';
                $action = 'list';
            } else {
                $message = 'Error updating article.';
                $message_type = 'error';
            }
        }
    } elseif (isset($_POST['delete_article'])) {
        if ($article->delete($id)) {
            $message = 'Article deleted successfully!';
            $message_type = 'success';
            $action = 'list';
        } else {
            $message = 'Error deleting article.';
            $message_type = 'error';
        }
    }
}

// Get article data for editing
$article_data = null;
if ($action == 'edit' && $id > 0) {
    $article_data = $article->getById($id);
    if (!$article_data) {
        $action = 'list';
        $message = 'Article not found.';
        $message_type = 'error';
    }
}

include 'includes/header.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">
                    <?php 
                    switch($action) {
                        case 'create': echo 'Create New Article'; break;
                        case 'edit': echo 'Edit Article'; break;
                        default: echo 'Manage Articles'; break;
                    }
                    ?>
                </h1>
                <p class="text-gray-600 mt-2">
                    <?php 
                    switch($action) {
                        case 'create': echo 'Write a new article for your blog'; break;
                        case 'edit': echo 'Update your article content'; break;
                        default: echo 'Create, edit, and manage your articles'; break;
                    }
                    ?>
                </p>
            </div>
            <?php if ($action == 'list'): ?>
            <a href="?action=create" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors">
                <i class="fas fa-plus mr-2"></i>New Article
            </a>
            <?php else: ?>
            <a href="articles.php" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                <i class="fas fa-arrow-left mr-2"></i>Back to List
            </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Messages -->
    <?php if ($message): ?>
    <div class="mb-6 p-4 rounded-lg alert-auto-hide <?php echo $message_type == 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'; ?>">
        <i class="fas <?php echo $message_type == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
        <?php echo htmlspecialchars($message); ?>
    </div>
    <?php endif; ?>

    <?php if ($action == 'list'): ?>
    <!-- Articles List -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6">
            <?php
            $all_articles = array_merge($article->getAll('published'), $article->getAll('draft'));
            if (empty($all_articles)):
            ?>
            <div class="text-center py-12">
                <i class="fas fa-newspaper text-6xl text-gray-400 mb-4"></i>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">No Articles Yet</h3>
                <p class="text-gray-600 mb-6">Start by creating your first article</p>
                <a href="?action=create" class="bg-primary text-white px-6 py-3 rounded-lg hover:bg-secondary transition-colors">
                    <i class="fas fa-plus mr-2"></i>Create Article
                </a>
            </div>
            <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Updated</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($all_articles as $article_item): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($article_item['title']); ?>
                                </div>
                                <div class="text-sm text-gray-500">
                                    <?php echo htmlspecialchars(substr($article_item['excerpt'] ?: strip_tags($article_item['content']), 0, 60)) . '...'; ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?php echo $article_item['status'] == 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                    <?php echo ucfirst($article_item['status']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo formatDate($article_item['created_at']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo formatDate($article_item['updated_at']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    <a href="?action=edit&id=<?php echo $article_item['id']; ?>" 
                                       class="text-primary hover:text-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <?php if ($article_item['status'] == 'published'): ?>
                                    <a href="../article.php?slug=<?php echo $article_item['slug']; ?>" 
                                       target="_blank" class="text-green-600 hover:text-green-900">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <?php endif; ?>
                                    <form method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this article?')">
                                        <input type="hidden" name="delete_article" value="1">
                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <?php elseif ($action == 'create' || $action == 'edit'): ?>
    <!-- Article Form -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6">
            <form method="POST">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Main Content -->
                    <div class="lg:col-span-2 space-y-6">
                        <div>
                            <label for="title" class="block text-sm font-medium text-gray-700 mb-2">
                                Article Title <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="title" name="title" required
                                   value="<?php echo $article_data ? htmlspecialchars($article_data['title']) : ''; ?>"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Enter article title">
                        </div>

                        <div>
                            <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-2">
                                Excerpt (Optional)
                            </label>
                            <textarea id="excerpt" name="excerpt" rows="3"
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                      placeholder="Brief description of the article..."><?php echo $article_data ? htmlspecialchars($article_data['excerpt']) : ''; ?></textarea>
                            <p class="text-sm text-gray-500 mt-1">This will be shown in article previews and search results.</p>
                        </div>

                        <div>
                            <label for="content" class="block text-sm font-medium text-gray-700 mb-2">
                                Content <span class="text-red-500">*</span>
                            </label>
                            <textarea id="content" name="content" rows="20" required
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                      placeholder="Write your article content here..."><?php echo $article_data ? htmlspecialchars($article_data['content']) : ''; ?></textarea>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Publish Settings -->
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Publish Settings</h3>

                            <div class="mb-4">
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
                                    Status
                                </label>
                                <select id="status" name="status"
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="draft" <?php echo ($article_data && $article_data['status'] == 'draft') ? 'selected' : ''; ?>>
                                        Draft
                                    </option>
                                    <option value="published" <?php echo ($article_data && $article_data['status'] == 'published') ? 'selected' : ''; ?>>
                                        Published
                                    </option>
                                </select>
                            </div>

                            <div class="flex space-x-3">
                                <button type="submit" name="<?php echo $action == 'edit' ? 'update_article' : 'create_article'; ?>"
                                        class="flex-1 bg-primary text-white py-2 px-4 rounded-lg hover:bg-secondary transition-colors">
                                    <i class="fas fa-save mr-2"></i>
                                    <?php echo $action == 'edit' ? 'Update' : 'Create'; ?>
                                </button>

                                <?php if ($action == 'edit'): ?>
                                <button type="button" onclick="showDeleteModal()"
                                        class="bg-red-600 text-white py-2 px-4 rounded-lg hover:bg-red-700 transition-colors">
                                    <i class="fas fa-trash"></i>
                                </button>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Article Info -->
                        <?php if ($article_data): ?>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Article Info</h3>
                            <div class="space-y-2 text-sm">
                                <div>
                                    <span class="font-medium">Created:</span>
                                    <span class="text-gray-600"><?php echo formatDate($article_data['created_at']); ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Updated:</span>
                                    <span class="text-gray-600"><?php echo formatDate($article_data['updated_at']); ?></span>
                                </div>
                                <div>
                                    <span class="font-medium">Slug:</span>
                                    <span class="text-gray-600"><?php echo htmlspecialchars($article_data['slug']); ?></span>
                                </div>
                                <?php if ($article_data['status'] == 'published'): ?>
                                <div class="pt-2">
                                    <a href="../article.php?slug=<?php echo $article_data['slug']; ?>"
                                       target="_blank"
                                       class="text-primary hover:text-secondary">
                                        <i class="fas fa-external-link-alt mr-1"></i>
                                        View Article
                                    </a>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Help -->
                        <div class="bg-blue-50 rounded-lg p-4">
                            <h3 class="text-lg font-medium text-blue-900 mb-2">
                                <i class="fas fa-info-circle mr-2"></i>
                                Tips
                            </h3>
                            <ul class="text-sm text-blue-800 space-y-1">
                                <li>• Use clear, descriptive titles</li>
                                <li>• Write engaging excerpts</li>
                                <li>• Save as draft to preview first</li>
                                <li>• Publish when ready to go live</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php endif; ?>
</div>

<script>
function showDeleteModal() {
    document.getElementById('deleteModal').classList.remove('hidden');
}

function hideDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}
</script>

<?php include 'includes/footer.php'; ?>
