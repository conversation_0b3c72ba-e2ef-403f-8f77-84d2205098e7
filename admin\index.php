<?php
require_once '../config/config.php';
require_once '../classes/Article.php';
require_once '../classes/Contact.php';

requireLogin();

$page_title = 'Dashboard';

// Get statistics
$article = new Article();
$contact = new Contact();

$total_articles = count($article->getAll('published'));
$draft_articles = count($article->getAll('draft'));
$unread_messages = $contact->getUnreadCount();
$recent_messages = $contact->getAll('unread');

include 'includes/header.php';
?>

<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p class="text-gray-600 mt-2">Welcome back, <?php echo htmlspecialchars($_SESSION['admin_username']); ?>!</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-newspaper text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Published Articles</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $total_articles; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-edit text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Draft Articles</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $draft_articles; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-red-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-envelope text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Unread Messages</p>
                    <p class="text-2xl font-semibold text-gray-900"><?php echo $unread_messages; ?></p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                        <i class="fas fa-eye text-white"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Views</p>
                    <p class="text-2xl font-semibold text-gray-900">1,234</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-900">Quick Actions</h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-2 gap-4">
                    <a href="articles.php?action=create" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                        <i class="fas fa-plus text-blue-600 text-xl mr-3"></i>
                        <span class="font-medium text-blue-600">New Article</span>
                    </a>
                    <a href="portfolio.php" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                        <i class="fas fa-briefcase text-green-600 text-xl mr-3"></i>
                        <span class="font-medium text-green-600">Manage Portfolio</span>
                    </a>
                    <a href="messages.php" class="flex items-center p-4 bg-red-50 rounded-lg hover:bg-red-100 transition-colors">
                        <i class="fas fa-envelope text-red-600 text-xl mr-3"></i>
                        <span class="font-medium text-red-600">View Messages</span>
                    </a>
                    <a href="../index.php" target="_blank" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                        <i class="fas fa-external-link-alt text-purple-600 text-xl mr-3"></i>
                        <span class="font-medium text-purple-600">View Website</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Messages -->
        <div class="bg-white rounded-lg shadow">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Messages</h2>
                    <a href="messages.php" class="text-primary hover:text-secondary text-sm">View All</a>
                </div>
            </div>
            <div class="p-6">
                <?php if (empty($recent_messages)): ?>
                <div class="text-center py-8">
                    <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
                    <p class="text-gray-600">No new messages</p>
                </div>
                <?php else: ?>
                <div class="space-y-4">
                    <?php foreach (array_slice($recent_messages, 0, 5) as $message): ?>
                    <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-gray-900">
                                <?php echo htmlspecialchars($message['name']); ?>
                            </p>
                            <p class="text-sm text-gray-600 truncate">
                                <?php echo htmlspecialchars(substr($message['message'], 0, 50)) . '...'; ?>
                            </p>
                            <p class="text-xs text-gray-500">
                                <?php echo formatDate($message['created_at']); ?>
                            </p>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- Recent Articles -->
    <div class="bg-white rounded-lg shadow">
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Recent Articles</h2>
                <a href="articles.php" class="text-primary hover:text-secondary text-sm">View All</a>
            </div>
        </div>
        <div class="p-6">
            <?php
            $recent_articles = array_merge($article->getAll('published', 3), $article->getAll('draft', 2));
            if (empty($recent_articles)):
            ?>
            <div class="text-center py-8">
                <i class="fas fa-newspaper text-4xl text-gray-400 mb-4"></i>
                <p class="text-gray-600 mb-4">No articles yet</p>
                <a href="articles.php?action=create" class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-secondary transition-colors">
                    Create Your First Article
                </a>
            </div>
            <?php else: ?>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Title</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php foreach ($recent_articles as $article_item): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">
                                    <?php echo htmlspecialchars($article_item['title']); ?>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    <?php echo $article_item['status'] == 'published' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                    <?php echo ucfirst($article_item['status']); ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <?php echo formatDate($article_item['created_at']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="articles.php?action=edit&id=<?php echo $article_item['id']; ?>" 
                                   class="text-primary hover:text-secondary mr-3">Edit</a>
                                <?php if ($article_item['status'] == 'published'): ?>
                                <a href="../article.php?slug=<?php echo $article_item['slug']; ?>" 
                                   target="_blank" class="text-green-600 hover:text-green-900">View</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
