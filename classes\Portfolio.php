<?php
// Check if we're in admin directory or root directory
if (file_exists('../config/database.php')) {
    require_once '../config/database.php';
} else {
    require_once 'config/database.php';
}

class Portfolio {
    private $conn;
    private $table_name = "portfolio_data";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getBySection($section) {
        $query = "SELECT * FROM " . $this->table_name . " 
                  WHERE section = :section AND is_active = 1 
                  ORDER BY order_index ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':section', $section);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function create($section, $title, $content = '', $image_url = '', $link_url = '', $order_index = 0) {
        $query = "INSERT INTO " . $this->table_name . " 
                  (section, title, content, image_url, link_url, order_index) 
                  VALUES (:section, :title, :content, :image_url, :link_url, :order_index)";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':section', $section);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':image_url', $image_url);
        $stmt->bindParam(':link_url', $link_url);
        $stmt->bindParam(':order_index', $order_index);
        
        return $stmt->execute();
    }

    public function update($id, $title, $content = '', $image_url = '', $link_url = '', $order_index = 0) {
        $query = "UPDATE " . $this->table_name . " 
                  SET title = :title, content = :content, image_url = :image_url, 
                      link_url = :link_url, order_index = :order_index 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        
        $stmt->bindParam(':id', $id);
        $stmt->bindParam(':title', $title);
        $stmt->bindParam(':content', $content);
        $stmt->bindParam(':image_url', $image_url);
        $stmt->bindParam(':link_url', $link_url);
        $stmt->bindParam(':order_index', $order_index);
        
        return $stmt->execute();
    }

    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }

    public function toggleActive($id) {
        $query = "UPDATE " . $this->table_name . " 
                  SET is_active = NOT is_active 
                  WHERE id = :id";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        
        return $stmt->execute();
    }
}
?>
