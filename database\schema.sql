-- Portfolio Database Schema
-- Import this file directly to phpMyAdmin
-- Make sure to create database 'portfolio_db' first or change the database name below

-- CREATE DATABASE IF NOT EXISTS portfolio_db;
-- USE portfolio_db;

-- Note: Remove the comments above if you want to auto-create the database
-- Or manually create the database 'portfolio_db' in phpMyAdmin first

-- Articles table
CREATE TABLE articles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    featured_image VARCHAR(255),
    status ENUM('draft', 'published') DEFAULT 'draft',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject <PERSON><PERSON><PERSON><PERSON>(255),
    message TEXT NOT NULL,
    status ENUM('unread', 'read', 'replied') DEFAULT 'unread',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Admin users table
CREATE TABLE admin_users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Portfolio data table (for dynamic content)
CREATE TABLE portfolio_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    section VARCHAR(50) NOT NULL,
    title VARCHAR(255),
    content TEXT,
    image_url VARCHAR(255),
    link_url VARCHAR(255),
    order_index INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert default admin user (password: admin123)
-- Password hash for 'admin123' using PHP password_hash()
INSERT INTO admin_users (username, email, password) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');

-- Insert sample portfolio data
INSERT INTO portfolio_data (section, title, content, order_index) VALUES
('skill', 'PHP', 'Backend development with PHP', 1),
('skill', 'JavaScript', 'Frontend development with JavaScript', 2),
('skill', 'MySQL', 'Database management', 3),
('skill', 'HTML/CSS', 'Web markup and styling', 4),
('education', 'Computer Science Degree', 'Bachelor of Computer Science - University Name (2020-2024)', 1),
('experience', 'Web Developer', 'Company Name - Full Stack Developer (2023-Present)', 1),
('organization', 'Tech Community', 'Member of local tech community', 1),
('activity', 'Open Source Contributor', 'Contributing to various open source projects', 1);
