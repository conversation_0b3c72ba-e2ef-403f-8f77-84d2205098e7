<?php
require_once 'config/config.php';
require_once 'classes/Contact.php';

$page_title = 'Contact Me';
$contact = new Contact();

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = sanitizeInput($_POST['name']);
    $email = sanitizeInput($_POST['email']);
    $subject = sanitizeInput($_POST['subject']);
    $message_content = sanitizeInput($_POST['message']);
    
    if (empty($name) || empty($email) || empty($message_content)) {
        $message = 'Please fill in all required fields.';
        $message_type = 'error';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $message = 'Please enter a valid email address.';
        $message_type = 'error';
    } else {
        if ($contact->create($name, $email, $subject, $message_content)) {
            $message = 'Thank you for your message! I\'ll get back to you soon.';
            $message_type = 'success';
        } else {
            $message = 'Sorry, there was an error sending your message. Please try again.';
            $message_type = 'error';
        }
    }
}

include 'includes/header.php';
?>

<!-- Contact Header -->
<section class="bg-gradient-to-r from-primary to-secondary text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl lg:text-5xl font-bold mb-4">Get In Touch</h1>
        <p class="text-xl text-blue-100 max-w-3xl mx-auto">
            Have a project in mind? Let's discuss how we can work together to bring your ideas to life.
        </p>
    </div>
</section>

<!-- Contact Content -->
<section class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Information -->
            <div>
                <h2 class="text-3xl font-bold text-gray-900 mb-8">Let's Connect</h2>
                
                <!-- Social Media -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Follow Me</h3>
                    <div class="flex space-x-4">
                        <a href="#" class="bg-gray-800 text-white p-4 rounded-full hover:bg-gray-700 transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="#" class="bg-blue-600 text-white p-4 rounded-full hover:bg-blue-700 transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="bg-blue-500 text-white p-4 rounded-full hover:bg-blue-600 transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="bg-pink-600 text-white p-4 rounded-full hover:bg-pink-700 transition-colors">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Contact Info -->
                <div class="mb-8">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Contact Information</h3>
                    <div class="space-y-4">
                        <div class="flex items-center">
                            <i class="fas fa-envelope text-primary mr-3"></i>
                            <span class="text-gray-600"><EMAIL></span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-phone text-primary mr-3"></i>
                            <span class="text-gray-600">+62 ************</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-map-marker-alt text-primary mr-3"></i>
                            <span class="text-gray-600">Jakarta, Indonesia</span>
                        </div>
                    </div>
                </div>
                
                <!-- Service Fees -->
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Service Fees</h3>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="space-y-4">
                            <div class="flex justify-between items-center border-b pb-2">
                                <span class="font-medium">Website Development</span>
                                <span class="text-primary font-semibold">$500 - $2000</span>
                            </div>
                            <div class="flex justify-between items-center border-b pb-2">
                                <span class="font-medium">Web Application</span>
                                <span class="text-primary font-semibold">$1000 - $5000</span>
                            </div>
                            <div class="flex justify-between items-center border-b pb-2">
                                <span class="font-medium">API Development</span>
                                <span class="text-primary font-semibold">$300 - $1500</span>
                            </div>
                            <div class="flex justify-between items-center border-b pb-2">
                                <span class="font-medium">Consultation (per hour)</span>
                                <span class="text-primary font-semibold">$50 - $100</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="font-medium">Maintenance (monthly)</span>
                                <span class="text-primary font-semibold">$100 - $500</span>
                            </div>
                        </div>
                        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                            <p class="text-sm text-blue-800">
                                <i class="fas fa-info-circle mr-2"></i>
                                Prices may vary based on project complexity and requirements. Contact me for a detailed quote.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Contact Form -->
            <div>
                <h2 class="text-3xl font-bold text-gray-900 mb-8">Send Message</h2>
                
                <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $message_type == 'success' ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-red-100 text-red-800 border border-red-200'; ?>">
                    <i class="fas <?php echo $message_type == 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
                <?php endif; ?>
                
                <form method="POST" class="bg-white rounded-lg shadow-md p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                                Name <span class="text-red-500">*</span>
                            </label>
                            <input type="text" id="name" name="name" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="Your full name">
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                Email <span class="text-red-500">*</span>
                            </label>
                            <input type="email" id="email" name="email" required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                   placeholder="<EMAIL>">
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">
                            Subject
                        </label>
                        <input type="text" id="subject" name="subject"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                               placeholder="Project inquiry, collaboration, etc.">
                    </div>
                    
                    <div class="mb-6">
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">
                            Message <span class="text-red-500">*</span>
                        </label>
                        <textarea id="message" name="message" rows="6" required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                                  placeholder="Tell me about your project or inquiry..."></textarea>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-primary text-white py-3 px-6 rounded-lg hover:bg-secondary transition-colors font-semibold">
                        <i class="fas fa-paper-plane mr-2"></i>
                        Send Message
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<?php include 'includes/footer.php'; ?>
