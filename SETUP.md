# Setup Portfolio Website - Panduan Singkat

## Langkah-langkah Setup:

### 1. Setup Database
1. Buka **phpMyAdmin** di browser (`http://localhost/phpmyadmin`)
2. <PERSON><PERSON> **"New"** untuk membuat database baru
3. Nama database: `portfolio_db`
4. <PERSON><PERSON> **"Create"**
5. Pilih database `portfolio_db` yang baru dibuat
6. Klik tab **"Import"**
7. Klik **"Choose File"** dan pilih file `database/schema.sql`
8. Klik **"Go"** untuk import

### 2. Konfigurasi Database (Opsional)
Jika menggunakan setting database yang berbeda, edit file `config/database.php`:
```php
define('DB_HOST', 'localhost');     // Host database
define('DB_NAME', 'portfolio_db');  // Nama database
define('DB_USER', 'root');          // Username database
define('DB_PASS', '');              // Password database
```

### 3. Akses Website
- **Website**: `http://localhost/p_a/`
- **Admin Panel**: `http://localhost/p_a/admin/`

### 4. Login Admin
- **Username**: `admin`
- **Password**: `admin123`

### 5. Upload Foto Profil
- Upload foto profil Anda ke folder `assets/images/`
- Nama file: `profile.jpg`
- Ukuran recommended: 400x400px atau lebih

## Kustomisasi Cepat:

### Update Informasi Personal
Edit file `index.php` bagian hero section:
```php
<h1 class="text-4xl lg:text-6xl font-bold mb-6">
    Hi, I'm <span class="text-accent">Nama Anda</span>
</h1>
<p class="text-xl lg:text-2xl mb-8 text-blue-100">
    Profesi Anda
</p>
```

### Update Contact Info
Edit file `contact.php` bagian contact information:
```php
<span class="text-gray-600"><EMAIL></span>
<span class="text-gray-600">+62 123 456 7890</span>
<span class="text-gray-600">Jakarta, Indonesia</span>
```

### Update Social Media Links
Edit file `includes/footer.php` dan `contact.php`:
```php
<a href="https://github.com/username">
<a href="https://linkedin.com/in/username">
<a href="https://twitter.com/username">
<a href="https://instagram.com/username">
```

## Fitur Admin Panel:

### Dashboard
- Statistik website
- Quick actions
- Recent messages
- Recent articles

### Articles
- **Create**: Buat artikel baru
- **Edit**: Edit artikel yang ada
- **Publish/Draft**: Atur status publikasi
- **Delete**: Hapus artikel

### Messages
- **View**: Lihat pesan dari contact form
- **Mark Read**: Tandai sebagai sudah dibaca
- **Mark Replied**: Tandai sebagai sudah dibalas
- **Delete**: Hapus pesan

### Portfolio Data
Data portfolio (skills, projects, education, dll) bisa diupdate melalui database atau bisa ditambahkan fitur admin untuk mengelolanya.

## Troubleshooting:

### Error Database Connection
1. Pastikan XAMPP/WAMP sudah running
2. Pastikan MySQL service aktif
3. Check konfigurasi di `config/database.php`
4. Pastikan database `portfolio_db` sudah dibuat

### Error 404 Not Found
1. Pastikan file `.htaccess` ada di root folder
2. Pastikan mod_rewrite aktif di Apache
3. Restart Apache service

### Admin Login Gagal
1. Pastikan database sudah diimport
2. Gunakan username: `admin`, password: `admin123`
3. Clear browser cache/cookies

### Gambar Tidak Muncul
1. Upload foto profil ke `assets/images/profile.jpg`
2. Pastikan folder `assets/images/` ada dan writable
3. Check path gambar di kode

## Tips:

1. **Backup Database**: Selalu backup database sebelum melakukan perubahan besar
2. **Update Password**: Ganti password admin default setelah setup
3. **SEO**: Update meta tags dan descriptions di header files
4. **Performance**: Optimize gambar sebelum upload
5. **Security**: Update PHP dan database secara berkala

## Support:

Jika ada masalah:
1. Check error log di XAMPP control panel
2. Pastikan semua file ada di tempatnya
3. Verify database connection
4. Check file permissions

Website siap digunakan! 🚀
